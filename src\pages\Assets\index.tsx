import type { TabItemType } from '@/components/OldTabs'
import type { I<PERSON><PERSON><PERSON><PERSON> } from '@/refactor/History/constans'
import type { CSSProperties, FC } from 'react'
import { Tabs } from '@/components/OldTabs'

import { TabHeader } from '@/pages/History/TabHeader'
import { Collection } from '@/refactor/History/Collection'
import { HistorySize } from '@/refactor/History/constants'
import { HDZoom } from '@/refactor/History/HDZoom'
import { History as HistoryComp } from '@/refactor/History/History'
import { Right } from '@/refactor/History/Right'
import { VideoHistory } from '@/refactor/History/VideoHistory'
import { useState } from 'react'

const History: FC = () => {
  /**
   * Tabs
   */
  const [selectedKey, setSelectedKey] = useState<IconKeys>('Collection')

  const listHeight: CSSProperties = {
    height: `calc(100% - ${HistorySize.tabHeight - HistorySize.gap}px)`,
  }

  const items: TabItemType<IconKeys>[] = useMemo(
    () => [
      {
        value: 'Collection',
        children: <Collection style={ listHeight } />,
        header: item => <TabHeader
          { ...item }
          selectedKey={ selectedKey }
          onClick={ () => setSelectedKey(item.value) }
        />,
      },
      {
        value: 'History',
        children: <HistoryComp style={ listHeight } />,
        header: item => <TabHeader
          { ...item }
          selectedKey={ selectedKey }
          onClick={ () => setSelectedKey(item.value) }
        />,
      },
      // {
      //   value: 'Video History',
      //   children: <VideoHistory style={ listHeight } />,
      //   header: (item) => <TabHeader
      //     { ...item }
      //     selectedKey={ selectedKey }
      //     onClick={ () => setSelectedKey(item.value) }
      //   />,
      // },
      {
        value: 'HD image',
        children: <HDZoom style={ listHeight } />,
        header: item => <TabHeader
          { ...item }
          selectedKey={ selectedKey }
          onClick={ () => setSelectedKey(item.value) }
        />,
      },
    ],
    [selectedKey],
  )

  return <div
    className="h-full w-full flex p-4"
    data-id="/p/history-loaded"
  >
    <div className="flex-1">
      <Tabs
        headerWrapClass="gap-4 flex"
        items={ items }
        activeKey={ selectedKey }
        onChange={ item => setSelectedKey(item.value) }
        headerStyle={ {
          marginBottom: HistorySize.gap,
        } }
        tabHeight={ 40 }
      />
    </div>

    {/* 工具栏 */ }
    {/* <Right
      style={ {
        width: HistorySize.rightWidth,
      } }
    /> */}

  </div>
}

export default History
History.displayName = 'History'
