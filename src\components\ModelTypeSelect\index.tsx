import { <PERSON>r, CascaderProps } from "antd"
import { FC, useEffect } from "react"
import { ModelTypeDO, ModelTypeVO } from "@/dts"
import { useModelType, useT } from "@/hooks"
import { findModelTypesById } from "@/utils"
import { padding } from '@/styles/variable'


const fieldNames: CascaderProps["fieldNames"] = {
  value: "id",
  label: "name",
  children: "children",
}

const ModelTypeSelect: FC<Props> = ({
  value,
  onChange,
  defaultTypeId,
  ...restProps
}) => {
  const t = useT()

  const { fetchData, data: typeTree } = useModelType()

  const handleChange: CascaderProps["onChange"] = (val, options) => {
    onChange?.(
      (val as ModelTypeDO["id"][]) || [],
      (options as ModelTypeVO[]) || [],
    )
  }

  useEffect(() => {
    if (defaultTypeId && 0 < typeTree.length) {
      const values = findModelTypesById(typeTree, defaultTypeId)
      if (!values) {
        return
      }
      onChange?.(
        values.map((o) => o.id),
        values,
      )
    }
  }, [typeTree, defaultTypeId, onChange])

  useEffect(() => {
    fetchData()
  }, [fetchData])


  return (
    <Cascader
      style={{ width: "100%", border: "none" }}
      options={typeTree}
      value={value}
      onChange={handleChange}
      fieldNames={fieldNames}
      placeholder={t("photog.select-type")}
      {...restProps}
    />
  )
}

export default memo(ModelTypeSelect)

interface Props extends Omit<CascaderProps, "value" | "onChange"> {
  value?: ModelTypeDO["id"][]
  onChange?: (value: ModelTypeDO["id"][], data: ModelTypeVO[]) => void
  defaultTypeId?: ModelTypeDO["id"]
}