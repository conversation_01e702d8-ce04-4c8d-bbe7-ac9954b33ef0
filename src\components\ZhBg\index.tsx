import { IS_ZH } from '@/config'
import { clsx } from 'clsx'
import { memo } from 'react'


export const ZhBg = memo<ZhBgProps>((
  {
    style,
    className,
    children
  }
) => {
  if (!IS_ZH) {
    return children
  }

  return <div
    className={ clsx(
      'ZhBgContainer absolute inset-0',
      className
    ) }
    style={ {
      backgroundImage: IS_ZH
        ? 'linear-gradient(80deg, #ffffff 0%, #fff 10%, #e6f7ff 100%)'
        : undefined,
      ...style
    } }
  >
    { children }
  </div>
})

ZhBg.displayName = 'ZhBg'

export type ZhBgProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
  & React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>