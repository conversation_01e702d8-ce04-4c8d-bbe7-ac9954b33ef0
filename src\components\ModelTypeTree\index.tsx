import { <PERSON>lapse, CollapseProps, Tree, Typography } from "antd"
import { FC, Key, useCallback, useEffect, useState } from "react"
import classNames from 'clsx'

import { useModelType } from "@/hooks"
import { ModelTypeDO, ModelTypeVO } from "@/dts"

import styles from "./index.module.less"
import { ChevronDown, ChevronRight } from 'lucide-react'

const fieldNames = {
  title: "name",
  key: "id",
  children: "children",
}

const panelKey = "model-type-tree-panel"

interface Props {
  className?: string
  onSelect: (d?: ModelTypeDO) => void
}

const ModelTypeTree: FC<Props> = (props) => {
  const { className, onSelect } = props

  const { fetchData, data: treeList } = useModelType()

  const [collapsed, setCollapsed] = useState<boolean>(true)
  const [selectedKey, setSelectedKey] = useState<ModelTypeDO["id"]>()

  const onChange = useCallback(() => {
    setCollapsed((c) => !c)
  }, [])

  const genExpandIcon = useCallback(
    () => (
      <>
        <Typography.Text>Type</Typography.Text>
        {collapsed ? (
          <ChevronDown className={styles["expand-icon"]} />
        ) : (
          <ChevronRight className={styles["expand-icon"]} />
        )}
      </>
    ),
    [collapsed],
  )

  const onSelectHandle = useCallback(
    async (_: Key[], { node }: { node: ModelTypeVO }) => {
      const { id } = node
      setSelectedKey((prev) => {
        if (id !== prev) {
          // @ts-ignore
          onSelect(node)
          return id
        }
        onSelect()
        return undefined
      })
    },
    [onSelect],
  )

  const genTreeList = useCallback(
    () => (
      <Tree
        blockNode
        showLine
        draggable={false}
        fieldNames={fieldNames}
        treeData={treeList}
        selectedKeys={selectedKey ? [selectedKey] : []}
        onSelect={onSelectHandle}
      />
    ),
    [treeList, selectedKey, onSelectHandle],
  )

  const genItems = useCallback(() => {
    return [
      {
        key: panelKey,
        children: genTreeList(),
      },
    ] as CollapseProps["items"]
  }, [genTreeList])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return (
    <Collapse
      className={classNames(styles["model-type-tree-collapse"], className)}
      expandIcon={genExpandIcon}
      items={genItems()}
      activeKey={collapsed ? [panelKey] : []}
      onChange={onChange}
      bordered={false}
      ghost
      collapsible="icon"
      size="small"
    />
  )
}

export default ModelTypeTree
