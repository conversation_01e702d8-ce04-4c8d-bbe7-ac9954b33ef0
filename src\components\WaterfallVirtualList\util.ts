import { round } from 'lodash-es'

export const columnGap = 12

export function computeColumnWidth(
  containerWidth: number,
  columns: number,
): number {
  return round((containerWidth - (columns - 1) * columnGap) / columns, 5)
}

const minColumnWidth = 24
/** 响应式计算瀑布流列数 */
export function computeMaxColumns(
  containerWidth: number,
  columns: number,
): number {
  if (columns < 2) {
    return 1
  }
  const computedWidth = computeColumnWidth(containerWidth, columns)
  if (minColumnWidth > computedWidth) {
    return computeMaxColumns(containerWidth, columns - 1)
  }
  return columns
}
