import i18n from 'i18next'
// import LanguageDetector from 'i18next-browser-languagedetector'
import { initReactI18next } from 'react-i18next'
import { resources } from './lang'

i18n
/**
 * 检测用户当前使用的语言
 * @link https://github.com/i18next/i18next-browser-languageDetector
 */
// .use(LanguageDetector)

  .use(initReactI18next)
  .init({
    debug: process.env.NODE_ENV === 'development',
    lng: 'en-US',
    fallbackLng: 'en-US',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      /** 设置语言检测的选项 */
      order: ['localStorage', 'navigator', 'querystring', 'cookie'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },
    resources,
  })

export default i18n
export const changeLanguage = (lng: string) => i18n.changeLanguage(lng)
export const getCurrentLanguage = () => i18n.language
