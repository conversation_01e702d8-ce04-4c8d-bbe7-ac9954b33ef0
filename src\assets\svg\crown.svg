<svg width="52" height="45" viewBox="0 0 52 45" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_i_1705_8515)">
    <ellipse cx="25.8274" cy="22.0395" rx="20.0501" ry="11.5556" fill="url(#paint0_linear_1705_8515)"/>
  </g>
  <g filter="url(#filter1_i_1705_8515)">
    <path d="M8.49003 35.8039L2.88281 12.6927L14.2672 21.1895L25.7365 3.51624L37.5456 21.1895L48.5901 12.6927L42.9829 35.8039C32.1776 41.4467 19.2953 41.4467 8.49003 35.8039Z" fill="url(#paint1_linear_1705_8515)"/>
  </g>
  <ellipse cx="3.05848" cy="11.8431" rx="3.05848" ry="3.05883" fill="url(#paint2_linear_1705_8515)"/>
  <ellipse cx="25.8241" cy="3.34643" rx="3.05848" ry="3.05883" fill="url(#paint3_linear_1705_8515)"/>
  <ellipse cx="48.9413" cy="11.8433" rx="3.05848" ry="3.05883" fill="url(#paint4_linear_1705_8515)"/>
  <ellipse opacity="0.5" cx="2.37107" cy="11.1636" rx="0.679663" ry="0.67974" fill="white"/>
  <ellipse opacity="0.5" cx="25.1406" cy="2.66692" rx="0.679663" ry="0.67974" fill="white"/>
  <ellipse opacity="0.5" cx="48.5937" cy="10.4837" rx="0.679663" ry="0.67974" fill="white"/>
  <g filter="url(#filter2_d_1705_8515)">
    <path d="M25.783 28.618L28.5151 19.442H31.1755L27.1371 31.0394H25.3848L25.783 28.618ZM23.266 19.442L25.9901 28.618L26.4043 31.0394H24.636L20.6215 19.442H23.266Z" fill="white"/>
  </g>
  <defs>
    <filter id="filter0_i_1705_8515" x="5.77734" y="10.4839" width="40.1016" height="23.7909" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.679701"/>
      <feGaussianBlur stdDeviation="0.509776"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.929412 0 0 0 0 0.596078 0 0 0 0 0.54902 0 0 0 1 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_1705_8515"/>
    </filter>
    <filter id="filter1_i_1705_8515" x="2.88281" y="3.51624" width="45.707" height="37.1995" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.679701"/>
      <feGaussianBlur stdDeviation="0.509776"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_1705_8515"/>
    </filter>
    <filter id="filter2_d_1705_8515" x="19.9414" y="19.442" width="11.9141" height="12.9568" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="0.679701"/>
      <feGaussianBlur stdDeviation="0.339851"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1705_8515"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1705_8515" result="shape"/>
    </filter>
    <linearGradient id="paint0_linear_1705_8515" x1="25.8274" y1="10.4839" x2="25.8274" y2="33.595" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FF9280"/>
      <stop offset="0.522761" stop-color="#D42A26"/>
    </linearGradient>
    <linearGradient id="paint1_linear_1705_8515" x1="25.7365" y1="3.51624" x2="25.7365" y2="44.8104" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFD871"/>
      <stop offset="1" stop-color="#FA8B06"/>
    </linearGradient>
    <linearGradient id="paint2_linear_1705_8515" x1="3.05848" y1="8.7843" x2="3.05848" y2="14.902" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F5C767"/>
      <stop offset="1" stop-color="#FCA430"/>
    </linearGradient>
    <linearGradient id="paint3_linear_1705_8515" x1="25.8241" y1="0.287598" x2="25.8241" y2="6.40525" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F5C767"/>
      <stop offset="1" stop-color="#FCA430"/>
    </linearGradient>
    <linearGradient id="paint4_linear_1705_8515" x1="48.9413" y1="8.78442" x2="48.9413" y2="14.9021" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F5C767"/>
      <stop offset="1" stop-color="#FCA430"/>
    </linearGradient>
  </defs>
</svg>