import type { StepProps } from '@/components/Steps'
import type { ParsedAgentData } from '@/worker/parseAgent'
import type { ChatMode, Msg } from './types'
import { createProxy } from '@/hooks'
import { genArr } from '@jl-org/tool'
import { genImgSize } from './constants'
import { genMsg } from './tool'

export const chatStore = createProxy({
  chatId: '',
  taskId: '',
  mode: 'awsShop' as ChatMode,
  showEditor: false,
  openWorkflow: false,
  showStep: false,

  loading: false,
  changeImging: false,

  showMode: 'searchResult' as ShowMode,
  selectedMode: ['searchResult', 'report'] as ShowMode[],

  agentSearchRes: [] as ParsedAgentData['searchResults'],
  searchDone: false,

  copyWrite: '',
  report: '',
  videoUrl: '',
  modelUrl: '',

  selectedMsg: undefined as undefined | Msg,
  /** 预览图 */
  previewMsgs: genArr<Msg>(genImgSize, () => genMsg()),

  stepStatus: {
    // 'img': 'wait',
    // 'video': 'process',
    // 'copyWrite': 'finish',
    // 'report': 'finish',
    // '3dModel': 'process',
  } as Record<ShowMode, StepProps['status']>,
})

export function resetChatStore() {
  chatStore.mode = 'awsShop'
  chatStore.showEditor = false
  chatStore.openWorkflow = false
  chatStore.showStep = false

  chatStore.showMode = 'searchResult'
  chatStore.searchDone = false

  chatStore.chatId = ''
  chatStore.taskId = ''
  chatStore.selectedMode = ['searchResult', 'report']

  chatStore.copyWrite = ''
  chatStore.report = ''
  chatStore.videoUrl = ''
  chatStore.modelUrl = ''

  chatStore.selectedMsg = undefined
  chatStore.previewMsgs = genArr<Msg>(genImgSize, () => genMsg())

  chatStore.stepStatus = {} as any
}

export type ShowMode =
  | 'img'
  | 'video'
  | 'copyWrite'
  | 'report'
  | '3dModel'
  | 'searchResult'
