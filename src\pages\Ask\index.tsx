import type {
  FormProps,
  ThemeConfig,
} from 'antd'
import type { FC } from 'react'
import { API_NODE } from '@/api'
import Logo from '@/components/Logo'

import SvgIcon from '@/components/SvgIcon'
import { god } from '@/god'
import { copyToClipboard } from '@jl-org/tool'
import {
  Button,
  ConfigProvider,
  Form,
  Input,
  Select,
  Typography,
} from 'antd'
import { useCallback, useState } from 'react'

const { Item } = Form
const theme = {
  token: {
    borderRadius: 6,
    Input: {
      borderRadius: 0,
    },
    Select: {
      borderRadius: 0,
    },
  },
} as ThemeConfig
const formProps: FormProps = {
  wrapperCol: { span: 16 },
  labelAlign: 'left',
  requiredMark: false,
}
const options = [
  {
    value: 1,
    label: '<100',
  },
  {
    value: 2,
    label: '100-200',
  },
  {
    value: 3,
    label: '200-500',
  },
  {
    value: 4,
    label: '500-1000',
  },
  {
    value: 5,
    label: '>1000',
  },
]

const Ask: FC = () => {
  const [form] = Form.useForm<FormValues>()

  const [loading, setLoading] = useState(false)

  const onShare = useCallback(() => {
    const url = location.href
    copyToClipboard(url)

    if (navigator.share) {
      navigator.share({
        title: 'PhotoG - Join the waitlist',
        text: 'Join the waitlist for our cutting-edge AIGC product',
        url,
      })
      return
    }

    god.messageSuccess({
      key: 'tip',
      content: 'Copy Success!',
    })
  }, [])

  const onFinish = useCallback(async (values: FormValues) => {
    setLoading(true)
    await API_NODE.askSubmit(values)
      .then(() => {
        god.messageSuccess({ key: 'tip', content: 'Success' })
        /** 返回上一个路由 */
        window.history.back()
      })
      .catch(() => { })
    setLoading(false)
  }, [])

  return <ConfigProvider theme={ theme }>
    <div className="relative h-full w-full overflow-auto">
      <header className="fixed left-0 top-0 z-10 h-16 w-full flex items-center justify-between bg-white/90 px-4 md:px-8">
        <Logo />
        <Button
          type="primary"
          icon={ <SvgIcon icon="share" /> }
          onClick={ onShare }
        >
          Share
        </Button>
      </header>

      <main className="min-h-screen w-full overflow-auto bg-[url('@/assets/image/ask/bg.webp')] bg-white bg-contain bg-right-top bg-no-repeat pb-8 pt-16">
        <div className="mx-auto max-w-[839px] w-full border border-[#e8e9e9] rounded-md bg-[url('@/assets/image/ask/bg2.webp')] bg-white bg-contain bg-center-top bg-no-repeat p-4 md:mt-8 md:p-[75px]">
          <h1 className="text-center text-2xl font-semibold">Join waitlist</h1>

          <div className="mt-[60px] space-y-9">
            <Typography.Text className="block font-semibold">
              Dear User：
            </Typography.Text>
            <Typography.Text className="block leading-6">
              We are excited to announce that our cutting-edge AIGC
              (Artificial Intelligence Generated Content) product is about to
              reach a new milestone...
            </Typography.Text>
          </div>

          <Form<FormValues>
            { ...formProps }
            className="mt-[60px]"
            form={ form }
            onFinish={ onFinish }
          >
            <div className="mb-6 bg-[#fafafa] p-4">
              <div className="mb-4">
                <Typography.Text className="text-2xl text-[#126dd7] font-bold">
                  01.
                </Typography.Text>
                <span className="ml-2">Your information</span>
              </div>
              {/* Form items remain the same */}
              <Item name="firstName" rules={ [{ required: true }] }>
                <Input placeholder="First name" />
              </Item>
              <Item name="lastName" rules={ [{ required: true }] }>
                <Input placeholder="Last name" />
              </Item>
              <Item name="email" rules={ [{ required: true, type: 'email' }] }>
                <Input placeholder="Work email" />
              </Item>
              <Item name="jobTitle" rules={ [{ required: true }] }>
                <Input placeholder="Job title" />
              </Item>
            </div>

            <div className="mb-6 bg-[#fafafa] p-4">
              <div className="mb-4">
                <Typography.Text className="text-2xl text-[#126dd7] font-bold">
                  02.
                </Typography.Text>
                <span className="ml-2">Company information</span>
              </div>
              {/* Form items remain the same */}
              <Item name="companyName" rules={ [{ required: true }] }>
                <Input placeholder="Company name" />
              </Item>
              <Item
                name="companySize"
                rules={ [{ required: true, type: 'number' }] }
              >
                <Select placeholder="Company size" options={ options } />
              </Item>
            </div>

            <div className="mb-6 bg-[#fafafa] p-4">
              <div className="mb-4">
                <Typography.Text className="text-2xl text-[#126dd7] font-bold">
                  03.
                </Typography.Text>
                <span className="ml-2">What are you hoping to get out of PhotoG?（Optional）</span>
              </div>
              {/* Form items remain the same */}
              <Item
                name="remark"
                rules={ [{ required: false }] }
                wrapperCol={ { span: 24 } }
              >
                <Input.TextArea
                  placeholder="You can say it simply"
                  autoSize={ { minRows: 3, maxRows: 6 } }
                />
              </Item>
            </div>

            <div className="mt-24 text-center">
              <Button
                type="primary"
                htmlType="submit"
                className="w-[120px]"
                loading={ loading }
              >
                Submit
              </Button>
              <div className="mt-2 text-xs leading-6">
                Thank you again for your participation and support!
              </div>
            </div>
          </Form>
        </div>
      </main>
    </div>
  </ConfigProvider>
}

export default Ask

type FormValues = Parameters<typeof API_NODE.askSubmit>[0]
