import type { FC } from 'react'
import { AnimateRoute } from '@/router/AnimateRoute'
import { LAYOUT_SIZE } from './constants'
import Header from './Header'
import { LeftAside } from './LeftAside'

const RootLayout: FC = () => {
  return (
    <div className="h-full w-full">
      <Header style={ {
        height: LAYOUT_SIZE.headerHeight,
      } } />

      <div
        className="flex"
        style={ {
          height: `calc(100% - ${LAYOUT_SIZE.headerHeight}px)`,
        } }>
        <LeftAside />

        <div className="flex-1">
          <AnimateRoute />
        </div>
      </div>
    </div>
  )
}

export default RootLayout
