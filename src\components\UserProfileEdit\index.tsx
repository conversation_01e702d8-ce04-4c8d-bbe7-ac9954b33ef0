import type { IndustryDO } from '@/api/user'
import type { Option } from '@/components/Select'
import type { UserInfoUpdateReq } from '@/dts'
import type { UploaderRef } from '../Uploader'
import { API_USER } from '@/api/user'
import { Button } from '@/components/Button'
import { Form, useForm } from '@/components/Form'
import { Icon } from '@/components/Icon'
import { Input, NumberInput } from '@/components/Input'
import { Radio, RadioGroup } from '@/components/Radio'
import { Select } from '@/components/Select'
import { Tooltip } from '@/components/Tooltip'
import { Gender } from '@/dts'
import { god } from '@/god'
import { updateUserInfo, userDetailStore } from '@/store/userStore'
import { excludeVals } from '@jl-org/tool'
import { motion } from 'framer-motion'
import { UserRoundPen } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useSnapshot } from 'valtio'
import { Uploader } from '../Uploader'

export const UserProfileEdit = memo(({ onClose }: UserProfileEditProps) => {
  const userinfo = useSnapshot(userDetailStore)
  const [industries, setIndustries] = useState<IndustryDO[]>([])
  const [loading, setLoading] = useState(false)

  /** 从API获取行业数据 */
  useEffect(() => {
    setLoading(true)
    API_USER.listAllIndustries()
      .then((res) => {
        setIndustries(res)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [])

  /** 行业选项转为Select组件所需格式 */
  const industryOptions: Option[] = industries.map(industry => ({
    value: industry.id.toString(),
    label: industry.name,
  }))

  /** 预览头像 */
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  /** 保存用户信息 */
  const handleSave = async (formData: any) => {
    /** 转换表单数据为API所需的格式 */
    const updateData = excludeVals({
      nickname: formData.username,
      gender: formData.gender === '男'
        ? Gender.MALE
        : Gender.FEMALE,
      avatarBase64: getBase64(formData.avatar),
      age: Number.parseInt(formData.age) || 0,
      industryId: Number.parseInt(formData.industry) || 0,
      company: formData.company || '',
      // phone: formData.phone || '',
    }, ['']) as UserInfoUpdateReq

    /** 调用API保存用户信息 */
    API_USER.updateBasicUserInfo(updateData)
      .then(() => {
        god.messageSuccess('Save User Info Success')
        updateUserInfo()
        onClose()
      })
  }

  return (
    <div className="mx-auto max-w-5xl rounded-lg bg-white px-4 py-4 shadow-md">
      <motion.div
        initial={ { opacity: 0, y: 10 } }
        animate={ { opacity: 1, y: 0 } }
        transition={ { duration: 0.3 } }
      >
        <h1 className="mb-8 text-2xl font-bold">个人资料</h1>

        <Form
          initialValues={ {
            avatar: userinfo.avatar || '',
            username: userinfo.nickname || '',
            gender: userinfo.gender === Gender.MALE
              ? '男'
              : userinfo.gender === Gender.FEMALE
                ? '女'
                : '男',
            age: userinfo.age || 0,
            phone: userinfo.phone || '',
            industry: userinfo.industryId.toString() || '',
            company: userinfo.company || '',
            credits: userinfo.totalCredits || 0,
          } }
          onSubmit={ handleSave }
        >
          <div className="mb-8">
            <h2 className="mb-6 text-xl font-medium">基本信息</h2>

            <div className="flex flex-col gap-8 md:flex-row">
              {/* 左侧头像上传 */ }
              <AvatarUploader previewImage={ previewImage } setPreviewImage={ setPreviewImage } />

              {/* 右侧个人信息表单 */ }
              <div className="grid grid-cols-1 flex-1 gap-6 md:grid-cols-2">
                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">用户名</label>
                  <Input
                    name="username"
                    placeholder="请输入用户名"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">邮箱</label>
                  <Input
                    value={ userinfo.email }
                    disabled
                    className="w-full bg-gray-50"
                  />
                  <p className="mt-1 text-xs text-gray-500">邮箱不可修改</p>
                </div>

                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">性别</label>
                  <RadioGroup name="gender" direction="horizontal">
                    <Radio value="男" label="男" />
                    <Radio value="女" label="女" />
                  </RadioGroup>
                </div>

                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">年龄</label>
                  <NumberInput
                    name="age"
                    placeholder="请输入年龄"
                    className="w-full"
                    min={ 1 }
                    max={ 150 }
                  />
                </div>

                {/* <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">电话</label>
                  <Input
                    name="phone"
                    placeholder="请输入联系电话"
                    className="w-full"
                    maxLength={ 15 }
                  />
                </div> */}

                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">行业</label>
                  <Select
                    name="industry"
                    options={ industryOptions }
                    placeholder="请选择行业"
                    className="w-full"
                    loading={ loading }
                  />
                </div>

                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">公司名称</label>
                  <Input
                    name="company"
                    placeholder="请输入公司名称"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-medium">积分</label>
                  <NumberInput
                    value={ userinfo.totalCredits || 0 }
                    disabled
                    className="w-full bg-gray-50"
                  />
                  <p className="mt-1 text-xs text-gray-500">积分不可修改</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-center gap-4">
            <Button
              type="submit"
              className="px-12"
              variant="primary"
              loading={ loading }
            >
              保存信息
            </Button>
            <Button
              onClick={ onClose }
              className="bg-gray-200 px-12 text-gray-800 hover:bg-gray-300"
              type="button"
            >
              取消
            </Button>
          </div>
        </Form>
      </motion.div>
    </div>
  )
})

function getBase64(base64: string) {
  if (base64.startsWith('http')) {
    return ''
  }

  if (!base64 || !base64.startsWith('data:image')) {
    return base64
  }

  return base64.split(',')[1]
}

/**
 * 头像上传组件
 */
const AvatarUploader = memo(({ previewImage, setPreviewImage }: { previewImage: string | null, setPreviewImage: (img: string | null) => void }) => {
  const uploadRef = useRef<UploaderRef>(null)
  const form = useForm()

  return (
    <div className="flex flex-col items-center">
      <div className="relative mb-4">
        <div className="h-32 w-32 flex items-center justify-center overflow-hidden border border-gray-200 rounded-full bg-gray-50">
          { previewImage
            ? (
                <img src={ previewImage } alt="头像" className="h-full w-full object-cover" />
              )
            : (
                form.state.values.avatar
                  ? (
                      <img src={ form.state.values.avatar } alt="头像" className="h-full w-full object-cover" />
                    )
                  : (
                      <img
                        src={ new URL('@/assets/svg/avatar.svg', import.meta.url).href }
                        alt="头像"
                        className="h-16 w-16"
                      />
                    )
              ) }
        </div>

        <div className="absolute bottom-0 right-2">
          <Uploader
            ref={ uploadRef }
            maxPixels={ {
              width: 2000,
              height: 2000,
            } }
            maxSize={ 1024 * 1024 * 2 }
            onChange={ (files) => {
              setPreviewImage(files?.[0]?.base64 || '')
              form.setFieldValue('avatar', files?.[0]?.base64 || '')
            } }
            accept="image/*"
          >
            <Tooltip content="ratio should be 1:1">
              <Icon
                icon={ UserRoundPen }
              />
            </Tooltip>
          </Uploader>
        </div>
      </div>
    </div>
  )
})

export interface UserProfileEditProps {
  onClose: () => void
}
