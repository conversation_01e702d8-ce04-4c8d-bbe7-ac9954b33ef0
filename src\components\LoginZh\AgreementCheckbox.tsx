import clsx from 'clsx'
import { motion } from 'framer-motion'
import { Check } from 'lucide-react'
import { memo } from 'react'
import { Checkmark } from '../Checkbox'

/**
 * 协议勾选组件
 */
export const AgreementCheckbox = memo(({
  checked,
  onChange,
  children,
  className,
}: AgreementCheckboxProps) => {
  return (
    <div className={ clsx('flex items-center', className) }>
      <motion.div
        className={ clsx(
          'mr-2 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full border transition-colors border-gray-300',
        ) }
        onClick={ () => onChange(!checked) }
        whileHover={ { scale: 1.1 } }
        whileTap={ { scale: 0.9 } }
      >
        <div className={ clsx(
          'size-4 flex items-center justify-center rounded-full transition-all duration-300',
          checked
            ? 'bg-primary'
            : 'bg-transparent',
        ) }>
          <Checkmark color="#fff" strokeWidth={ 5 } size={ 16 } show={ checked }></Checkmark>
        </div>
      </motion.div>
      <div className="text-xs text-gray-500">
        { children }
      </div>
    </div>
  )
})

AgreementCheckbox.displayName = 'AgreementCheckbox'

export interface AgreementCheckboxProps {
  /**
   * 是否选中
   */
  checked: boolean
  /**
   * 选中状态变更回调
   */
  onChange: (checked: boolean) => void
  /**
   * 协议文本内容
   */
  children: React.ReactNode
  /**
   * 自定义类名
   */
  className?: string
}
