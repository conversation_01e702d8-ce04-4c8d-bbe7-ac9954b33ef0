import type { ModalProps } from 'antd'
import { TransparentModal } from '@/components/CloseModal/TransparentModal'
import cx from 'clsx'
import { memo } from 'react'
import { showEditorWidth } from '../constants'
import Editor from './Editor'

export const EditorModal = memo<EditorModalProps>((
  {
    style,
    className,
    ...rest
  },
) => {
  return <TransparentModal
    className={ cx(
      'EditorModalContainer',
      className,
    ) }
    style={ style }
    { ...rest }
    width={ showEditorWidth + 213 }
    destroyOnClose
  >
    <Editor />
  </TransparentModal>
})

EditorModal.displayName = 'EditorModal'

export type EditorModalProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
& ModalProps
