import type { ParsedAgentData } from '@/worker/parseAgent'
import type { ShowMode } from '../store'
import { AgentA<PERSON> } from '@/api/AgentApi'
import { KeepAlive } from '@/components/KeepAlive'
import { god } from '@/god'
import { downloadByData } from '@jl-org/tool'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { memo } from 'react'
import { productWidth, titleMap } from '../constants'
import { chatStore } from '../store'
import AgentDataViewer from './AgentDataViewer'
import { D3Model } from './D3Model'
import ImageGallery2 from './ImageGallery2'
import { Switch } from './Switch'
import TextContent from './TextContent'
import { Video } from './Video'

function ProductVisualizer({ className }: ProductVisualizerProps) {
  const snap = chatStore.use()
  const headerHeight = 50

  const [loading, setLoading] = useState(false)

  return (<div
    className={ clsx(
      'ProductVisualizer overflow-hidden rounded-xl p-6 relative bg-white shadow-xl',
      className,
    ) }
    style={ {
      width: productWidth,
      minWidth: productWidth,
    } }
  >
    {/* 顶部选项卡 */ }
    <div
      className="flex border-b border-gray-200"
      style={ {
        height: headerHeight,
      } }
    >
      <Switch />
    </div>

    <motion.div
      initial={ { opacity: 0, x: 20 } }
      animate={ { opacity: 1, x: 0 } }
      transition={ { duration: 0.5 } }
      className={ clsx(
        'pt-4 rounded-xl',
        'flex gap-4 flex-1 flex-col',
      ) }
      style={ {
        height: `calc(100% - ${headerHeight}px)`,
      } }
    >
      <KeepAlive active={ snap.showMode === 'img' }>
        <ImageGallery2 className="flex-1" />
      </KeepAlive>
      <KeepAlive active={ snap.showMode === 'video' }>
        <Video className="flex-1 overflow-y-auto" />
      </KeepAlive>
      <KeepAlive active={ snap.showMode === '3dModel' }>
        <D3Model className="flex-1 overflow-y-auto" />
      </KeepAlive>
      {/* <KeepAlive active={ snap.showMode === 'copyWrite' }>
        <TextContent txt={ snap.copyWrite } className="flex-1" />
      </KeepAlive> */}
      <KeepAlive active={ snap.showMode === 'report' }>
        <TextContent txt={ snap.report } className="flex-1"
          disabled={ loading }
          onDownload={ async () => {
            if (!chatStore.taskId) {
              god.messageWarn('Not Found Task ID')
              return
            }

            god.messageLoading('Downloading...')
            setLoading(true)

            try {
              const blob = await AgentApi.downloadResult(chatStore.taskId)
              if (blob.type.includes('json')) {
                god.messageWarn('Data not ready')
                return
              }
              downloadByData(blob, 'result.zip')
            }
            catch (error) {
              god.messageError('Download Failed')
            }
            finally {
              setLoading(false)
              god.messageDismiss()
            }
          } }
          noData={ snap.searchDone }
        />
      </KeepAlive>

      <KeepAlive active={ snap.showMode === 'searchResult' }>
        <AgentDataViewer
          searchResults={ snap.agentSearchRes || [] }
          className="flex-1 overflow-y-auto"
          noData={ snap.searchDone }
        />
      </KeepAlive>
    </motion.div>
  </div>)
}

export default memo(ProductVisualizer)

export type ProductVisualizerProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
