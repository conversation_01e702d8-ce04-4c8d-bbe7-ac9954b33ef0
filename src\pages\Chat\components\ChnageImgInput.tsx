import { AiChatTask<PERSON>tatus, Chat<PERSON>pi, IsShow, RecordRole, SessionType, SubmitEnum } from '@/api/ChatApi'
import { ChatGPTApi } from '@/api/ChatGPTApi'
import { god } from '@/god'
import { useClickOutside, useT } from '@/hooks'
import { updateUserInfo } from '@/store/userStore'
import { getImgNetUrl } from '@/utils'
import { deepClone } from '@jl-org/tool'
import { Button } from 'antd'
import { clsx } from 'clsx'
import { Send } from 'lucide-react'
import { memo } from 'react'
import { ChatEventBus } from '../constants'
import { chatStore } from '../store'

export const ChnageImgInput = memo<ChnageImgInputProps>((
  {
    style,
    className,
    onCancel,
  },
) => {
  const snap = chatStore.use()
  const t = useT()
  const elRef = useRef<HTMLDivElement>(null)
  const [message, setMessage] = useState('')

  useClickOutside([elRef], () => {
    if (chatStore.showEditor) {
      onCancel?.()
    }
  })

  async function onChangeImg() {
    if (chatStore.changeImging) {
      god.messageWarn('Image generation in progress ...')
      return
    }
    if (!chatStore.chatId) {
      god.messageError('No ChatId')
      return
    }

    const imageUrl = chatStore.selectedMsg?.files[0].base64
    if (!imageUrl)
      return

    const index = chatStore.previewMsgs.findIndex(item => item.id === chatStore.selectedMsg?.id)

    // @ts-ignore
    delete chatStore.selectedMsg?.files[0].file
    chatStore.selectedMsg = deepClone(chatStore.selectedMsg)
    chatStore.selectedMsg!.files[0].base64 = ''
    chatStore.previewMsgs[index].files[0].base64 = ''
    chatStore.changeImging = true
    chatStore.showEditor = false

    await ChatApi.submitTask(chatStore.chatId, [SubmitEnum.ChangeImage])
      .then(() => updateUserInfo())

    try {
      setMessage('')
      const imgUrls = await ChatGPTApi.genImg({
        prompt: message,
        imageUrl,
      })

      ChatApi.confirmTask(chatStore.chatId, [{
        type: SubmitEnum.ChangeImage,
        status: AiChatTaskStatus.Finished,
      }])

      if (imgUrls[0]) {
        chatStore.selectedMsg!.files[0].base64 = imgUrls[0]
        chatStore.selectedMsg!.files[0].rawUrl = imgUrls[0]
        chatStore.previewMsgs[index].files[0].base64 = imgUrls[0]
        chatStore.previewMsgs[index].files[0].rawUrl = imgUrls[0]

        await ChatApi.recordSession({
          chatId: chatStore.chatId,
          aiChatDataList: [{
            content: '',
            originalImageUrls: [await getImgNetUrl(imgUrls[0])],
            isShow: IsShow.Show,
            role: RecordRole.Ai,
            type: SessionType.Image,
          }],
        })

        ChatEventBus.emit('reloadSidebar')
      }
      else {
        console.log('Image generation failed')
        god.messageError('Image generation failed')
      }
    }
    catch (error) {
      console.log(error)
      ChatApi.confirmTask(chatStore.chatId, [{
        type: SubmitEnum.ChangeImage,
        status: AiChatTaskStatus.Failed,
      }])
      god.messageError('Image generation failed')
    }
    finally {
      chatStore.changeImging = false
    }
  }

  return <div
    className={ clsx(
      'ChnageImgInputContainer h-24 flex items-center gap-2 bg-white/50 backdrop-blur rounded-xl',
      className,
    ) }
    style={ style }
    ref={ elRef }
  >
    <textarea
      rows={ 3 }
      value={ message }
      onChange={ e => setMessage(e.target.value) }
      placeholder={ t('chat.change-image-placeholder') }
      className={ clsx(
        'bg-lightBg/40 outline-none resize-none p-3 h-full rounded-xl flex-1',
      ) }
      style={ {
        width: `calc(100% - 6rem)`,
      } }
    />

    <Button
      type="primary"
      disabled={ !message || snap.changeImging }
      onClick={ onChangeImg }
      className="rounded-xl py-6"
    >
      <Send size={ 20 } />
    </Button>
  </div>
})

ChnageImgInput.displayName = 'ChnageImgInput'

export type ChnageImgInputProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  onCancel?: () => void
}
