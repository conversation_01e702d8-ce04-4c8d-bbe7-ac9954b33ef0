import type { CSSProperties } from 'react'
import { CloseModal } from '@/components/CloseModal'
import { isMobileDevice } from '@/config'
import { useMemoFn } from '@/hooks'
import { userStore } from '@/store/userStore'
import { ConfigProvider } from 'antd'
import classnames from 'clsx'
import { memo } from 'react'
import { styled } from 'styled-components'
import { useSnapshot } from 'valtio'
import { ForgotPwd } from './ForgotPwd'
import { Login } from './Login'
import { SignUp } from './SignUp'

const MyModal = styled(CloseModal)`
  .ant-modal-content {
    border-radius: 20px !important;
  }
`

function _Login({
  style,
  className,
}: LoginProps) {
  const [mode, setMode] = useState<'signUp' | 'forgot' | 'signIn'>('signIn')

  useEffect(() => {
    if (isMobileDevice) {
      setMode('signUp')
    }
  }, [])

  const snap = useSnapshot(userStore)
  const close = useMemoFn(() => {
    userStore.loginModal = false
  })

  return <ConfigProvider theme={ {
    components: {
      Form: {
        itemMarginBottom: 16,
      },
    },
  } }>
    <MyModal
      className={ classnames(
        className,
      ) }
      style={ style }
      open={ snap.loginModal }
      width={ 380 }
      onCancel={ close }
    >
      <div className="px-2">
        { mode === 'signIn' && !isMobileDevice && <Login
          onForgot={ () => setMode('forgot') }
          onSignUp={ () => setMode('signUp') }
        /> }

        { mode === 'forgot' && !isMobileDevice && <ForgotPwd
          onSignIn={ () => setMode('signIn') }
        /> }

        { (mode === 'signUp' || isMobileDevice) && <SignUp
          onSignIn={ () => !isMobileDevice && setMode('signIn') }
        /> }
      </div>
    </MyModal>
  </ConfigProvider>
}

const LoginComp = memo<LoginProps>(_Login)

export default LoginComp
_Login.displayName = 'Login'

export type LoginProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode
}
