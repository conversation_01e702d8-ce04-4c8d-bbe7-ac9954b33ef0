import { memo, type CSSProperties, ReactNode } from 'react'
import classnames from 'clsx'
import styles from './likeIcon.module.scss'
import SvgIcon from '../SvgIcon'


function _LikeIcon({
  style,
  className,
  like,
  onLike
}: LikeIconProps) {

  return (<div
    style={style}
    className={classnames(styles.wrap)}
  >

    {/* <div className={classnames(styles.itemWrap)}>
      <HeartFilled onClick={() => onLike?.(!like)}
        className={like ? styles.heartActive : styles.heart}
        style={{
          fontSize: 20
        }} />
    </div> */}

    <div className={classnames(className, styles.itemWrap)}
      onClick={() => onLike?.(!like)}>
      {
        like
          ? <SvgIcon icon='like' />
          : <SvgIcon icon='unlike' />
      }
    </div>

  </div>)
}
_LikeIcon.displayName = 'LikeIcon'

export const LikeIcon = memo(_LikeIcon)

export type LikeIconProps = {
  className?: string
  style?: CSSProperties

  like?: boolean
  onLike?: (like: boolean) => void
}
