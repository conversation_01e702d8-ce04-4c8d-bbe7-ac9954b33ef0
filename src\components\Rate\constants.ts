export const rateOptions = [
  { rate: '16:10', src: new URL('@/assets/image/aiDrawing/16-10.svg', import.meta.url).href },
  { rate: '16:9', src: new URL('@/assets/image/aiDrawing/16-9.svg', import.meta.url).href },
  { rate: '4:3', src: new URL('@/assets/image/aiDrawing/4-3.svg', import.meta.url).href },
  { rate: '3:2', src: new URL('@/assets/image/aiDrawing/3-2.svg', import.meta.url).href },
  { rate: '2:1', src: new URL('@/assets/image/aiDrawing/2-1.svg', import.meta.url).href },
  { rate: '1:1', src: new URL('@/assets/image/aiDrawing/1-1.svg', import.meta.url).href },
  { rate: '1:2', src: new URL('@/assets/image/aiDrawing/1-2.svg', import.meta.url).href },
  { rate: '2:3', src: new URL('@/assets/image/aiDrawing/2-3.svg', import.meta.url).href },
  { rate: '3:4', src: new URL('@/assets/image/aiDrawing/3-4.svg', import.meta.url).href },
  { rate: '9:16', src: new URL('@/assets/image/aiDrawing/9-16.svg', import.meta.url).href },
  { rate: '10:16', src: new URL('@/assets/image/aiDrawing/10-16.svg', import.meta.url).href },
] as const

export type RateType = typeof rateOptions[number]['rate']

export const defaultRateOptions = [
  '1:1',
  '16:9',
  '4:3',
  '2:1'
] as const