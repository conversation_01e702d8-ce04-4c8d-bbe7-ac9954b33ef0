import clsx from 'clsx'
import { motion } from 'framer-motion'
import { ChevronDown } from 'lucide-react'
import { memo, useState } from 'react'

/**
 * 手机号输入组件
 */
export const PhoneInput = memo(({
  value,
  onChange,
  countryCode = '+86',
  onCountryCodeChange,
  placeholder = '请输入中国大陆手机号',
  className,
}: PhoneInputProps) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  /** 常用国家/地区代码 */
  const countryCodes = [
    { code: '+86', name: '中国大陆' },
    { code: '+852', name: '中国香港' },
    { code: '+886', name: '中国台湾' },
    { code: '+1', name: '美国/加拿大' },
    { code: '+44', name: '英国' },
    { code: '+81', name: '日本' },
    { code: '+82', name: '韩国' },
  ]

  /** 切换下拉菜单状态 */
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  /** 选择国家/地区代码 */
  const selectCountryCode = (code: string) => {
    if (onCountryCodeChange) {
      onCountryCodeChange(code)
    }
    setIsDropdownOpen(false)
  }

  return (
    <div className={ clsx('relative rounded-lg bg-[#F4F9FE] p-4 shadow-sm', className) }>
      <div className="flex items-center">
        <div className="relative">
          <div
            className="flex items-center text-sm text-gray-700"
            // onClick={ toggleDropdown }
          >
            <span>{ countryCode }</span>
            {/* <ChevronDown className="ml-1 h-4 w-4 text-gray-400" /> */}
          </div>

          { isDropdownOpen && (
            <motion.div
              className="absolute left-0 top-8 z-10 w-40 rounded-lg bg-white py-1 shadow-lg"
              initial={ { opacity: 0, y: -10 } }
              animate={ { opacity: 1, y: 0 } }
              exit={ { opacity: 0, y: -10 } }
              transition={ { duration: 0.2 } }
            >
              { countryCodes.map(country => (
                <button
                  key={ country.code }
                  className={ clsx(
                    'block w-full px-4 py-2 text-left text-sm hover:bg-gray-100',
                    country.code === countryCode
                      ? 'bg-gray-50 font-medium'
                      : '',
                  ) }
                  onClick={ () => selectCountryCode(country.code) }
                >
                  { country.name }
                  { ' ' }
                  { country.code }
                </button>
              )) }
            </motion.div>
          ) }
        </div>

        {/* 分隔线 */ }
        <span className="mx-3 text-gray-300">|</span>

        {/* 手机号输入框 */ }
        <input
          type="tel"
          className="flex-1 bg-transparent text-sm outline-none placeholder:text-gray-400"
          placeholder={ placeholder }
          value={ value }
          onChange={ e => onChange(e.target.value) }
        />
      </div>
    </div>
  )
})

PhoneInput.displayName = 'PhoneInput'

export interface PhoneInputProps {
  /**
   * 手机号值
   */
  value: string
  /**
   * 手机号变更回调
   */
  onChange: (value: string) => void
  /**
   * 国家/地区代码
   * @default '+86'
   */
  countryCode?: string
  /**
   * 国家/地区代码变更回调
   */
  onCountryCodeChange?: (code: string) => void
  /**
   * 占位符文本
   * @default '请输入中国大陆手机号'
   */
  placeholder?: string
  /**
   * 自定义类名
   */
  className?: string
}
