import clsx from 'clsx'
import { motion } from 'framer-motion'
import { memo } from 'react'

/**
 * 切换开关组件
 */
export const ToggleSwitch = memo(({
  checked,
  onChange,
  className,
  size = 'default',
}: ToggleSwitchProps) => {
  /** 尺寸配置 */
  const sizes = {
    small: {
      container: 'h-4 w-8',
      thumb: 'h-3 w-3 top-0.5',
      movement: { on: 14, off: 2 },
    },
    default: {
      container: 'h-5 w-10',
      thumb: 'h-4 w-4 top-0.5',
      movement: { on: 20, off: 4 },
    },
    large: {
      container: 'h-6 w-12',
      thumb: 'h-5 w-5 top-0.5',
      movement: { on: 26, off: 6 },
    },
  }

  const currentSize = sizes[size]

  return (
    <motion.div
      className={ clsx(
        'relative cursor-pointer rounded-full transition-colors',
        currentSize.container,
        checked
          ? 'bg-blue-500'
          : 'bg-gray-300',
        className,
      ) }
      onClick={ () => onChange(!checked) }
      whileHover={ { scale: 1.05 } }
      whileTap={ { scale: 0.95 } }
    >
      <motion.div
        className={ clsx(
          'absolute rounded-full bg-white shadow-sm',
          currentSize.thumb,
        ) }
        animate={ {
          x: checked
            ? currentSize.movement.on
            : currentSize.movement.off,
        } }
        transition={ {
          type: 'spring',
          stiffness: 500,
          damping: 30,
        } }
      />
    </motion.div>
  )
})

ToggleSwitch.displayName = 'ToggleSwitch'

export interface ToggleSwitchProps {
  /**
   * 是否选中
   */
  checked: boolean
  /**
   * 选中状态变更回调
   */
  onChange: (checked: boolean) => void
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 开关尺寸
   * @default 'default'
   */
  size?: 'small' | 'default' | 'large'
}
