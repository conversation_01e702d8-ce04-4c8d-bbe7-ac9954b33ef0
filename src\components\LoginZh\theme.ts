/**
 * 登录页面主题配置
 */

/** 低饱和度颜色 */
export const lowSaturationColors = {
  /** 背景色 */
  background: {
    primary: '#F8FBFF', // 主背景色
    secondary: '#F4F9FE', // 次要背景色，用于输入框等
    tertiary: '#F0F5FA', // 第三级背景色
  },
  /** 文本色 */
  text: {
    primary: '#1A1A1A', // 主文本色
    secondary: '#4A4A4A', // 次要文本色
    tertiary: '#6E6E6E', // 第三级文本色
    disabled: '#9E9E9E', // 禁用状态文本色
  },
  /** 边框色 */
  border: {
    light: '#E0E7EF', // 浅色边框
    medium: '#C5D0DB', // 中等边框
    dark: '#A9B6C3', // 深色边框
  },
}

/** 高饱和度强调色 */
export const accentColors = {
  primary: '#3B82F6', // 主强调色（蓝色）
  secondary: '#10B981', // 次要强调色（绿色）
  danger: '#EF4444', // 危险色（红色）
  warning: '#F59E0B', // 警告色（橙色）
}

/** 阴影配置 */
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
}

/** 动画配置 */
export const animations = {
  /** 过渡时间 */
  transition: {
    fast: 0.15,
    normal: 0.3,
    slow: 0.5,
  },
  /** 弹簧动画配置 */
  spring: {
    gentle: { type: 'spring', stiffness: 200, damping: 25 },
    bounce: { type: 'spring', stiffness: 300, damping: 10 },
    stiff: { type: 'spring', stiffness: 500, damping: 30 },
  },
}

export default {
  lowSaturationColors,
  accentColors,
  shadows,
  animations,
}
