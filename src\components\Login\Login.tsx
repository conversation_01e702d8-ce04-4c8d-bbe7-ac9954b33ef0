import type { CSSProperties } from 'react'
import Logo from '@/components/Logo'
import { googleLogin } from '@/utils/auth'
import { Button, Divider, Form, Input, Typography } from 'antd'
import classnames from 'clsx'
import { memo } from 'react'
import { Agree } from './Agree'
import { useLogin } from './useLogin'

const { Text } = Typography

function InnerLogin({
  style,
  className,
  onForgot,
  onSignUp,
}: LoginProps) {
  const [form] = Form.useForm()
  const {
    loading,
    onEmailLogin,
  } = useLogin(form)

  return <Form
    className={ classnames(
      className,
    ) }
    form={ form }
    style={ style }
    onFinish={ onEmailLogin }
  >
    <div className="h-12 text-center">
      <Text className="font-semibold">Create an account or Login</Text>
    </div>

    <Button
      onClick={ googleLogin }
      type="primary"
      icon={ <img
        src={ new URL('@/assets/svg/google.svg', import.meta.url).href }
        alt="google"
        className="size-4"
      /> }
      className="w-full !h-10 !rounded-xl"
    >
      Continue with Google
    </Button>
    <Divider className="mb-4 !px-4">or</Divider>

    {/* <div className='flex justify-center items-center mb-4'>
      <Logo />
    </div> */}

    <div className="mb-4">
      <p className="text-blue-500">Continue with email</p>
    </div>

    <Form.Item name="email" rules={ [{ required: true, message: 'Please input your email!' }] }>
      <Input
        placeholder="Email"
        className="h-10 !rounded-xl"
        allowClear
        data-id="loginEmail"
      />
    </Form.Item>
    <Form.Item name="password" rules={ [{ required: true, message: 'Please input your password!' }] }>
      <Input.Password
        placeholder="Password"
        className="h-10 !rounded-xl"
        allowClear
        data-id="loginPassword"
      />
    </Form.Item>

    <Form.Item>
      <Button
        type="primary"
        className="w-full !h-10 !rounded-xl"
        loading={ loading }
        htmlType="submit"
        data-id="loginSubmit"
      >
        Login
      </Button>
    </Form.Item>

    <div className="mt-4 flex justify-between gap-4">
      <div
        className="flex-1 cursor-pointer py-2 text-center text-blue-500 transition duration-300 !bg-lightBg hover:opacity-70"
        onClick={ onSignUp }
        data-id="loginSignUp"
      >
        Sign Up
      </div>
      <div
        className="flex-1 cursor-pointer py-2 text-center transition duration-300 !bg-lightBg hover:opacity-70"
        onClick={ onForgot }
        data-id="loginForgot"
      >
        Forgot Password
      </div>
    </div>

    <Agree />
  </Form>
}

export const Login = memo<LoginProps>(InnerLogin)
Login.displayName = 'Login'

export type LoginProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode

  onForgot?: () => void
  onSignUp?: () => void
}
