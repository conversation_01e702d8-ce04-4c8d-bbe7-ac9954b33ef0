/* eslint-disable react-hooks/rules-of-hooks */
import { useRefresh } from './lifecycle'

function useRefState<T>(
  initState: T,
  refreshFn = useRefresh(),
) {
  const state = useRef<T>(initState)
  const proxyState = useMemo(
    () => new Proxy(state, {
      get(target, prop, receiver) {
        return Reflect.get(target, prop, receiver)
      },
      set(target, prop, value, receiver) {
        if (prop !== 'current' || target[prop] === value) {
          return true
        }

        const flag = Reflect.set(target, prop, value, receiver)
        refreshFn()
        return flag
      },
    }),
    [refreshFn],
  )

  return proxyState
}

export function useGenRefState() {
  const refreshFn = useRefresh()

  return <T>(initState: T) => {
    return useRefState(initState, refreshFn)
  }
}
