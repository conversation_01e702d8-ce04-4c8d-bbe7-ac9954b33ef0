import Logo from '@/components/Logo'
import SvgIcon from '@/components/SvgIcon'
import { IS_PROD } from '@/config'
import { RechargeTypeEnum } from '@/dts'
import { onMounted, useNavi, useT } from '@/hooks'
import { updateUserInfo, userDetailStore } from '@/store/userStore'
import { Popover } from 'antd'
import cn from 'clsx'
import { motion } from 'framer-motion'
import React from 'react'
import { useSnapshot } from 'valtio'
import { UserInfo } from './UserInfo'

type NavItem = {
  name: string
  pathname: string
}

type NavbarProps = {
  className?: string
  style?: React.CSSProperties
}

const Navbar = React.memo(({ className, style }: NavbarProps) => {
  const to = useNavi()
  const t = useT()
  const userinfo = useSnapshot(userDetailStore)
  const hasAvatar = userinfo.avatar && userinfo.avatar !== ''

  onMounted(() => {
    updateUserInfo()
  })

  const navItems: NavItem[] = IS_PROD
    ? [
        { name: t('layout.nav-agent'), pathname: '/p/chat' },
        { name: t('layout.nav-square'), pathname: '/p/history' },
        { name: t('layout.nav-photo'), pathname: '/p/photog' },
        { name: t('layout.nav-video'), pathname: '/p/video' },
        // { name: t('layout.nav-items'), pathname: '/p/models' },
        { name: t('layout.nav-assets'), pathname: '/p/assets' },
        // { name: t('layout.nav-distribution'), pathname: '/p/distribution' },
      ]
    : [
        { name: t('layout.nav-agent'), pathname: '/p/chat' },
        { name: t('layout.nav-square'), pathname: '/p/history' },
        { name: t('layout.nav-photo'), pathname: '/p/photog' },
        { name: t('layout.nav-video'), pathname: '/p/video' },
        // { name: t('layout.nav-items'), pathname: '/p/models' },
        { name: t('layout.nav-assets'), pathname: '/p/assets' },
        { name: t('layout.nav-lab'), pathname: '/p/lab' },
      ]

  return (
    <motion.header
      initial={ { y: -20, opacity: 0 } }
      animate={ { y: 0, opacity: 1 } }
      transition={ { duration: 0.5, ease: 'easeOut' } }
      className={ cn(
        'w-full px-4 md:px-8 flex items-center bg-white/50 justify-between border-b border-gray-100 backdrop-blur-sm relative',
        className,
      ) }
      style={ style }
    >
      <Logo className="mr-12 flex items-center" logoColor="#333"></Logo>

      <nav className="absolute left-1/2 min-w-96 flex transform gap-14 -translate-x-1/2">
        { navItems.map(item => (
          <NavLink key={ item.name } item={ item } />
        )) }
      </nav>

      <div className="flex items-center gap-4">
        <div
          className={ cn(
            `flex gap-2 cursor-pointer bg-white rounded-full overflow-hidden`,
            'transition hover:bg-innerBg p-2',
          ) }
          onClick={ () => to('/p/innerPricing', { state: { isLoginMode: false } }) }
        >
          <SvgIcon icon="credit3" noFill className="h-6 w-6"></SvgIcon>
        </div>

        <div className="flex items-center gap-2">
          <div
            className={ cn(
              `flex gap-2 cursor-pointer bg-white rounded-full overflow-hidden`,
              'transition hover:bg-innerBg p-2',
            ) }
            onClick={ () => to('/p/innerPricing', { state: { isLoginMode: false, type: RechargeTypeEnum.MONTHLY } }) }
          >
            <SvgIcon icon="charge" noFill className="h-6 w-6"></SvgIcon>
          </div>
          <span className="text-sm">{ userinfo.totalCredits }</span>
        </div>

        <Popover
          content={ <UserInfo></UserInfo> }
        >
          <div
            className={ cn(
              'relative size-8 cursor-pointer overflow-hidden border border-gray-300 rounded-full border-solid bg-white',
              hasAvatar
                ? ''
                : 'p-1.2',
            ) }
          >
            <img
              src={ userinfo.avatar || new URL('@/assets/svg/avatar.svg', import.meta.url).href }
              alt=""
              className={ cn(
                {
                  'size-full object-cover': hasAvatar,
                },
              ) }
            />
          </div>
        </Popover>
      </div>
    </motion.header>
  )
})

Navbar.displayName = 'Navbar'

const NavLink = React.memo(({ item }: { item: NavItem }) => {
  const to = useNavi()
  const { pathname } = useLocation()

  return (
    <motion.a
      className={ cn(
        'relative px-1 py-2 text-sm font-medium transition-colors',
        item.pathname === pathname
          ? 'text-blue-500'
          : 'text-gray-800 hover:text-gray-900',
      ) }
      whileHover={ { scale: 1.05 } }
      whileTap={ { scale: 0.95 } }
      onClick={ () => to(item.pathname as any) }
      data-id={ item.pathname }
    >
      <span className="font-bold">
        { item.name }
      </span>

      { item.pathname === pathname && (
        <motion.div
          layoutId="activeNavIndicator"
          className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500"
          initial={ { opacity: 0 } }
          animate={ { opacity: 1 } }
          transition={ { duration: 0.2 } }
        />
      ) }
    </motion.a>
  )
})

NavLink.displayName = 'NavLink'

export default Navbar
