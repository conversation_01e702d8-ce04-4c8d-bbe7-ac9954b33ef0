import type { FileItem } from '@/components/Uploader'
import type { LogEntry, ParsedAgentData } from '@/worker/parseAgent'
import type { Msg } from './types'
import type { UnzippedFile } from './unzip'
import { AgentApi } from '@/api/AgentApi'
import { Role } from '@/api/ChatGPTApi'
import { ZhBg } from '@/components/ZhBg'
import { useT } from '@/hooks'
import { composeBase64 } from '@/utils'
import ParseAgentWorker from '@/worker/parseAgent?worker'
import { formatDate } from '@jl-org/tool'
import { Chat } from './components/Chat'
import { EditorModal } from './components/EditorModal'
import { chatStore } from './store'
import { genMsg } from './tool'
import { useUnzip } from './unzip'
import { useChatData } from './useChatData'
import { useTestData } from './useTestData'
import '@/styles/css/github-light.css'

function App() {
  const {
    snap,
    isAWS,
    messages,
    isLoading,

    setMessages,
    setIsLoading,

    sendMsg,
    sendTink,
    updateThink,
    thinkDone,
    thinkData,

    loadingAnswer,
    updateAnswer,
    onFail,

    handleSendMessage,
  } = useChatData()

  const workerRef = useRef<Worker | null>(null)
  const answerIdRef = useRef<string | null>(null)
  const logsRef = useRef<LogEntry[]>([])
  const t = useT()

  const { unzipFile } = useUnzip({
    onSuccess(files) {
      handleZip(files)
    },
  })

  const processWorkerData = useCallback(
    (data: ParsedAgentData) => {
      if (!answerIdRef.current)
        return

      /** 更新搜索结果到store */
      chatStore.agentSearchRes.push(...data.searchResults)
    },
    [updateAnswer],
  )

  function handleZip(files: UnzippedFile[]) {
    for (const file of files) {
      if (file.name.endsWith('md')) {
        chatStore.report = file.data
        chatStore.showMode = 'report'
      }
    }

    if (!chatStore.report)
      return

    for (const file of files) {
      if (chatStore.report.includes(file.name)) {
        const escapedFileName = file.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        const reg = new RegExp(`!\\[${escapedFileName}\\]\\s*\\(${escapedFileName}\\)`, 'g')
        chatStore.report = chatStore.report.replace(reg, `<img src="${composeBase64(file.data)}" alt="${file.name}" />`)
      }
    }
    console.log(chatStore.report)
  }

  /** 初始化worker */
  useEffect(() => {
    workerRef.current = new ParseAgentWorker()

    /** 监听来自Worker的消息 */
    workerRef.current.onmessage = (event: MessageEvent<ParsedAgentData>) => {
      processWorkerData(event.data)
      console.log('Worker message:', event.data)
    }

    /** 监听Worker错误 */
    workerRef.current.onerror = (error) => {
      console.error('Worker error:', error)

      const errorMessage = `<div style="color: #e53e3e; background-color: #fed7d7; padding: 8px; border-radius: 4px; margin: 8px 0;">
      <strong>❌ 错误:</strong> 处理数据时发生错误，请重试。
      <pre style="margin-top: 8px; white-space: pre-wrap;">${error.message || '未知错误'}</pre>
    </div>`

      setMessages(prev => [
        ...prev,
        genMsg({
          content: errorMessage,
          type: 'embed',
        }),
      ])

      onFail()
    }

    return () => {
      workerRef.current?.terminate()
    }
  }, [processWorkerData, setIsLoading, updateAnswer, onFail, setMessages])

  const onMsg = async (content: string, files: FileItem[]) => {
    /** 清空日志 */
    logsRef.current = []
    setIsLoading(true)
    chatStore.searchDone = false
    setMessages([])

    /** 创建用户消息 */
    const userMessage: Msg = {
      ...genMsg(),
      content,
      role: Role.User,
      type: 'answer',
      files: files.map(item => ({
        ...item,
        rawUrl: '',
      })),
    }
    setMessages(prev => [...prev, userMessage])

    /** 添加临时加载消息 */
    answerIdRef.current = loadingAnswer()
    chatStore.openWorkflow = true

    try {
      const { taskId } = await AgentApi.createTask({
        prompt: content,
        images: files.map(f => f.file),
      })
      // const taskId = '1917111169785663488'
      chatStore.taskId = taskId

      const { cancel, promise } = await AgentApi.streamTaskEvents(
        taskId,
        (data) => {
          chatStore.showMode = 'searchResult'

          for (const item of data.logs) {
            // @ts-ignore
            if (item.status?.includes?.('timeout')) {
              onError(t('layout.timeout'))
              chatStore.searchDone = true
              setIsLoading(false)
              cancel()
              return
            }
          }

          /** 将新日志添加到已处理列表 */
          if (data.logs?.length > 0) {
            logsRef.current = [...logsRef.current, ...data.logs]

            /** 更新显示 */
            updateAnswer(answerIdRef.current!, 'Agent 处理中...', {
              type: 'embed',
              logs: logsRef.current,
            })
          }

          try {
            /** 将数据发送给worker处理 */
            workerRef.current?.postMessage(data.searchRes)
          }
          catch (error) {
            console.error('Error parsing SSE data:', error)
          }
        },
        (error) => {
          console.error('SSE error:', error)
          onFail()
        },
      )

      await promise
      useUnzipFile(taskId)

      /** 完成 */
      updateAnswer(answerIdRef.current, 'Agent 处理完成', {
        type: 'embed',
        logs: [
          ...logsRef.current,
          {
            level: 'INFO',
            timestamp: formatDate(),
            source: 'Agent',
            message: 'Agent Finished',
          },
        ],
      })
    }
    catch (error) {
      console.error('Task creation error:', error)
      onFail()
    }
    finally {
      chatStore.searchDone = true
      setIsLoading(false)
    }
  }

  async function useUnzipFile(taskId: string) {
    const blob = await AgentApi.downloadResult(taskId)
    const r = new FileReader()
    r.readAsArrayBuffer(blob)
    r.onload = (e) => {
      unzipFile(e.target!.result as ArrayBuffer)
    }
  }

  function onError(err: string) {
    const errorMessage = `<div style="color: #e53e3e; background-color: #fed7d7; padding: 8px; border-radius: 4px; margin: 8px 0;">
      <strong>❌ 错误:</strong> 处理数据时发生错误，请重试。
      <pre style="margin-top: 8px; white-space: pre-wrap;">${err}</pre>
    </div>`

    setMessages(prev => [
      ...prev,
      genMsg({
        content: errorMessage,
        type: 'embed',
        role: Role.Assistant,
      }),
    ])

    onFail()
  }

  const { onTestMsg } = useTestData({
    loadingAnswer,
    updateAnswer,
    onFail,
    setIsLoading,
    setMessages,
  })

  return (
    <div className="relative size-full bg-lightBg">
      <ZhBg>
        <Chat
          setMessages={ setMessages }
          messages={ messages }
          onSendMsg={ onMsg }
          onLoadMore={ () => { } }
          isLoading={ isLoading }
          hasMore={ false }
          className="mx-auto h-full w-full"
        />
      </ZhBg>

      <EditorModal
        open={ snap.showEditor }
        onCancel={ () => chatStore.showEditor = false }
      />
    </div>
  )
}

export default App
