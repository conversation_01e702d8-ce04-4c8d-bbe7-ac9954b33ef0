import { type CSSProperties } from "react"
import Logo from "@/components/Logo"
import { useNavi } from '@/hooks'
import { HEADER_ID } from '../constants'
import { loadHdArr, resetHdArr, studioStore } from '@/refactor/Photog/StudioList/store'
import { useSnapshot } from 'valtio'
import { loadTrainArr, modelStore, resetTrainArr } from '@/refactor/Models/store'
import { HdTask } from '@/refactor/Photog/StudioList/components/HDTask'
import { TrainTask } from '@/refactor/Models/components/TrainTask'
import { onMounted } from '@/hooks'
import { Info } from './Info'


const Header = ({ style }: Props) => {
  const studioSnap = useSnapshot(studioStore)
  const modelSnap = useSnapshot(modelStore)
  const to = useNavi()

  onMounted(() => {
    resetHdArr()
    loadHdArr()

    resetTrainArr()
    loadTrainArr()
  })

  return (
    <header
      id={HEADER_ID}
      className={`bg-white flex items-center justify-between px-4`}
      style={{
        borderBottom: '1px solid #e8e9e9',
        ...style,
      }}
    >
      <Logo onClick={() => to("/")} />

      <div className='flex gap-2'>
        {studioSnap.hdArr.length > 0 && <HdTask />}
        {modelSnap.trainArr.length > 0 && <TrainTask />}
        {/* <Info /> */}
      </div>

    </header>
  )
}

export default Header

type Props = {
  style?: CSSProperties
}
