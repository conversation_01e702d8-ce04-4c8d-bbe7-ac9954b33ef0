.title {
  $border-c: #EA4B19;
  $dot-size: 4px;

  position: relative;
  width: fit-content;

  p {
    white-space: nowrap;
    margin-bottom: 0;
    line-height: 1.2;
  }

  .line,
  .line2,
  .dot,
  .dot2 {
    position: absolute;
    inset: 0;
  }

  // 上下的线条
  .line::before,
  .line::after {
    content: "";
    position: absolute;
    left: -5%;
    top: 0;
    width: 120%;
    height: 1px;
    background-color: $border-c;
  }

  .line::after {
    top: unset;
    bottom: 0;
    width: 113%;
    height: 1px;
    background-color: $border-c;
  }

  // 左右的线条
  .line2::before,
  .line2::after {
    content: "";
    position: absolute;
    top: -11%;
    left: 0;
    height: 125%;
    width: 1px;
    background-color: $border-c;
  }

  .line2::after {
    left: unset;
    right: 0;
  }

  // 小点
  .dot::before,
  .dot::after {
    content: "";
    position: absolute;
    left: $dot-size * -.35;
    top: $dot-size * -.5;
    width: $dot-size;
    height: $dot-size;
    border-radius: 50%;
    background-color: $border-c;
  }

  .dot::after {
    left: unset;
    right: $dot-size * -.4;
    top: $dot-size * -.4;
    width: $dot-size;
    height: $dot-size;
    border-radius: 50%;
    background-color: $border-c;
  }

  .dot2::before,
  .dot2::after {
    content: "";
    position: absolute;
    left: $dot-size * -.2;
    bottom: $dot-size * -.4;
    width: $dot-size;
    height: $dot-size;
    border-radius: 50%;
    background-color: $border-c;
  }

  .dot2::after {
    left: unset;
    right: $dot-size * -.4;
    bottom: $dot-size * -.4;
    width: $dot-size;
    height: $dot-size;
    border-radius: 50%;
    background-color: $border-c;
  }
}