import { IS_ZH } from '@/config'
import { memo } from 'react'

export const VersionWrapper = memo<VersionWrapperProps>((
  {
    isZh = IS_ZH,
    foreignComponent,
    zhComponent,
  },
) => {
  const Component = isZh
    ? zhComponent
    : foreignComponent
  return (
    <>
      { Component }
    </>
  )
})

VersionWrapper.displayName = 'VersionWrapper'

export type VersionWrapperProps = {
  isZh?: boolean
  zhComponent: React.ReactNode
  foreignComponent: React.ReactNode
}
