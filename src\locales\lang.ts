import type { Resource } from 'i18next'

export enum SupportedLanguages {
  ZH_CN = 'zh-CN',
  EN_US = 'en-US',
}

const { zh, en } = getLang()

export const resources: Resource = {
  [SupportedLanguages.EN_US]: { translation: en },
  [SupportedLanguages.ZH_CN]: { translation: zh },
}

function getLang() {
  const enData = import.meta.glob('./en-US/*.json', { eager: true })
  const zhData = import.meta.glob('./zh-CN/*.json', { eager: true })

  const enModules: Record<string, any> = {}
  const zhModules: Record<string, any> = {}

  Object.entries(enData).forEach(([filePath, module]) => {
    const namespace = filePath.split('/').pop()!.replace('.json', '')
    if (namespace === 'index')
      return

    enModules[namespace] = (module as any).default
  })

  Object.entries(zhData).forEach(([filePath, module]) => {
    const namespace = filePath.split('/').pop()!.replace('.json', '')
    if (namespace === 'index')
      return

    zhModules[namespace] = (module as any).default
  })

  return { en: enModules, zh: zhModules }
}
