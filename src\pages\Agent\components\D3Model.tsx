import { Loading } from '@/components/Loading'
import { god } from '@/god'
import { genDirLight, loadGLTF, ThreeScene } from '@/utils'
import { applyAnimation, DEG_45, downloadByUrl, matchProtocol } from '@jl-org/tool'
import cx from 'clsx'
import { memo } from 'react'
import * as THREE from 'three'
import { chatStore } from '../store'
import { Btns } from './Btns'

export const D3Model = memo<D3ModelProps>((
  {
    style,
    className,
  },
) => {
  const [loading, setLoading] = useState(false)
  const snap = chatStore.use()
  const threeRef = useRef<HTMLDivElement>(null)
  const threeScene = useRef<ThreeScene>()
  const lastModel = useRef<THREE.Group<THREE.Object3DEventMap>>()
  const dirLight = useRef<THREE.DirectionalLight>()
  const light = useRef<THREE.AmbientLight>()

  function init() {
    if (!threeRef.current)
      return

    const { width, height } = threeRef.current.getBoundingClientRect()
    const ratio = width / height

    threeScene.current = new ThreeScene({
      camera: new THREE.PerspectiveCamera(
        75, /** 视角 */
        ratio, /** 比例 */
        0.1, /** 近平面 */
        50, /** 远平面 */
      ),
      renderer: new THREE.WebGLRenderer({
        alpha: false,
        antialias: true,
        preserveDrawingBuffer: true,
      }),
    })

    /**
     * light
     */
    dirLight.current = genDirLight({
      color: '#D9E4F2',
      intensity: 4.5,
    })
    light.current = new THREE.AmbientLight('#fff', 0.8)
    threeScene.current.addToSence(dirLight.current, light.current)

    /**
     * Setting
     */
    const three = threeScene.current!
    three.setBgc(0x333333)
    three.camera.position.set(0, 0, 4.7)

    three.addOrbitControls()
    three.appendTo(threeRef.current!)
    three.lookAt([0, 0, 0])
    three.enableShadow()

    applyAnimation(() => {
      three.render()
    })

    return () => three.dispose()
  }

  async function loadModel() {
    // const url = 'https://tripo-data.cdn.bcebos.com/tcli_cc25514543394888ba8cd4776b18a5f0/20250309/29f32ec7-00ef-4eee-bfe5-b78bbbc08e9e/tripo_pbr_model_29f32ec7-00ef-4eee-bfe5-b78bbbc08e9e.glb?auth_key=1741564800-D0cJR6Kv-0-530f390e410c10fa2d426572b46bb5b8'

    const url = chatStore.modelUrl
    if (!url)
      return
    if (lastModel.current) {
      threeScene.current?.removeFromSence(lastModel.current)
    }
    if (!threeScene.current)
      return

    setLoading(true)
    const three = threeScene.current

    try {
      const modelUrl = await fetch(matchProtocol(url))
        .then(res => res.blob())
        .then((blob) => {
          return URL.createObjectURL(blob)
        })

      loadGLTF(
        modelUrl,
        {
          onLoad: (glb) => {
            const { scene } = glb

            scene.rotateY(DEG_45)
            dirLight.current?.lookAt(scene.position)
            dirLight.current?.position.set(4.5, 5.3, 0)

            three.addToSence(scene)
            three.orbitControls?.target.copy(scene.position)
            lastModel.current = scene

            URL.revokeObjectURL(modelUrl)
          },
          onProgress: (e) => {
            console.log(`加载进度：${e.loaded / e.total}`)
            e.loaded / e.total === 1 && setLoading(false)
          },
        },
      )
    }
    catch (e) {
      god.messageError('model load failed')
    }
  }

  useEffect(
    init,
    [],
  )

  useEffect(
    () => {
      loadModel()
    },
    [snap.modelUrl],
  )

  return <div
    className={ cx(
      'D3ModelContainer relative flex flex-col h-full animate-fade-in-right',
      className,
    ) }
    style={ {
      animationTimingFunction: 'ease-in-out',
      animationDuration: '.6s',
      ...style,
    } }
    onDoubleClick={ () => {
      threeScene.current?.fullScreen()
    } }
  >
    <div
      ref={ threeRef }
      className="size-full"
    >
    </div>

    <Loading loading={ loading }></Loading>
    <Btns
      btnClassName={ cx(
        loading || !snap.modelUrl
          ? 'cursor-wait'
          : 'cursor-pointer',
      ) }
      onDonwload={ () => {
        if (loading || !snap.modelUrl)
          return
        try {
          downloadByUrl(snap.modelUrl)
        }
        catch (error) {
          god.messageError('Url is expired')
        }
      } }
    />
  </div>
})

D3Model.displayName = 'D3Model'

export type D3ModelProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
