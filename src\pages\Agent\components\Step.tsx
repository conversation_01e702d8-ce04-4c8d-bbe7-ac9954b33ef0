import type { StepProps } from '@/components/Steps'
import { Steps } from '@/components/Steps'
import cx from 'clsx'
import { memo } from 'react'
import { descMap, titleMap } from '../constants'
import { chatStore } from '../store'
import { iconMap } from './Icons'

export const Step = memo<StepsProps>((
  {
    style,
    className,
  },
) => {
  const snap = chatStore.use()

  const items = useMemo(
    () => snap.selectedMode.map<StepProps>((item) => {
      return {
        title: titleMap()[item].replace('Marketing', ''),
        status: snap.stepStatus[item],
        icon: iconMap[item],
        desc: descMap[item],
      }
    }),
    [snap.selectedMode, snap.stepStatus],
  )

  return <div
    className={ cx(
      'StepsContainer mx-auto transition-all duration-200',
      className,
    ) }
    style={ {
      ...style,
    } }
  >
    <Steps
      expandDirection="up"
      className="relative z-50 bg-white"
      showLinkLine={ false }
      items={ items }
    />
  </div>
})

Steps.displayName = 'Steps'

export type StepsProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
