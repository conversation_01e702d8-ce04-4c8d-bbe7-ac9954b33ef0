import { applyAnimation, Clock } from '@jl-org/tool'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useUpdateEffect } from './lifecycle'

export function useDefer(stopFrame: number) {
  const [curFrame, setCurFrame] = useState<number>(0)

  useEffect(() => {
    const stop = applyAnimation(() => {
      setCurFrame((curFrame) => {
        const nextFrame = curFrame + 1

        if (nextFrame > stopFrame) {
          stop()
        }

        return nextFrame
      })
    })

    return stop
  }, [stopFrame])

  return (frame: number) => curFrame >= frame
}

export function useTimer(
  fn: VoidFunction,
  {
    durationMS = 1000,
    immediate = true,
  }: TimerOpts = {},
) {
  const tick = useRef(durationMS / durationMS)

  const [startController, setStartController] = useState(0)
  const stopFn = useRef(() => { })
  const effectFn = immediate
    ? useEffect
    : useUpdateEffect

  effectFn(() => {
    tick.current = durationMS / durationMS
    const clock = new Clock()

    stopFn.current()
    stopFn.current = applyAnimation(() => {
      if (clock.elapsedMS / durationMS <= tick.current) {
        return
      }

      tick.current++
      fn?.()
    })

    return stopFn.current
  }, [durationMS, fn, startController])

  return {
    start: useCallback(() => setStartController(v => v + 1), []),
    stop: useCallback(() => stopFn.current(), []),
  }
}

interface TimerOpts {
  durationMS?: number
  immediate?: boolean
}
