import type { RateProps } from '.'
import { rateOptions } from './constants'
import classnames from 'clsx'

export const RateExtra = memo<RateProps>(({
  rate,
  setRate,
}) => <div className='flex flex-col'>
    { rateOptions.map(option => (
      <div className={ classnames(
        'flex gap-4 hover:bg-lightBg cursor-pointer p-2',
        rate === option.rate
          ? 'bg-lightBg'
          : 'opacity-50'
      ) }
        onClick={ () => setRate(option.rate) }
        key={ option.rate }
      >

        <div className='w-10'>
          <img src={ option.src } alt="" height={ 20 } />
        </div>

        <p className='w-8'>{ option.rate }</p>

        <div className='flex justify-end w-16'>
          {
            rate === option.rate && <img width={ 16 }
              src={ new URL('@/assets/image/aiDrawing/checked.svg', import.meta.url).href } />
          }
        </div>
      </div>
    )) }
  </div>)