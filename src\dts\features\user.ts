export interface UserDO {
  id: string
  username: string
  nickname: string
  gender: Gender
  email: string
  phone: string
  avatar: string | null
  description: string
  pwdResetTime: string
  registrationDate: string
  deptId: number
  deptName: string
  permissions: string[]
  roles: string[]
  totalCredits: number
  membershipPlan: string
}

export enum Gender {
  UNKNOWN = 0,
  MALE = 1,
  FEMALE = 2,
}

export type UserInfoUpdateReq = {
  /** 昵称 */
  nickname: string
  /** 性别（0：未知；1：男；2：女） */
  gender: Gender
  /** 头像URL */
  avatarBase64: string | null
  /** 年龄 */
  age: number
  /** 行业ID */
  industryId: number
  /** 公司名称 */
  company: string
  // phone: string
}
