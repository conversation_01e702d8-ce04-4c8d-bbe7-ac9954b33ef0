export enum RechargeTypeEnum {
  /** 月付 */
  MONTHLY = 'monthly',
  /** 年付 */
  YEARLY = 'yearly',
  /** unknown */
  REPLENISH = 'replenish',
  /** 兑换码，不参与传参 */
  REDEEM = 'redeem',
}

export enum RechargePlanTypeEnum {
  /** 基础套餐 */
  BASIC = 'basic',
  /** 标准套餐 */
  STANDARD = 'standard',
  /** 专业套餐 */
  PROFESSIONAL = 'professional',
  /** 企业套餐 */
  ENTERPRISE = 'enterprise',
}

/** 充值套餐 */
export interface RechargePlanDO {
  /** 套餐类型 */
  type: RechargePlanTypeEnum
  /** payment类型 */
  paymentType: PaymentTypeEnum
  /** 价格 */
  price: number
  /** 年估价 */
  yearlyAnnualPrice: number
  /** 积分 */
  credits: number
  /** 每个生图任务支持的生图数量 */
  numLimitOfImageGenerateTask: number
  /** 生成的图片是否有水印 */
  watermark: boolean
}

export type RechargePlanLabelKeys =
  | 'planTypeLabel'
  | 'priceLabel'
  | 'descLabel'
  | 'creditsLabel'
  | 'gcLabel'
  | 'watermarkLabel'
  | 'storageLabel'
  | 'templateLabel'
  | 'enterpriseLabel'

export type RechargePlanVO = RechargePlanDO &
  Record<RechargePlanLabelKeys, string> & {
    rechargeType: RechargeTypeEnum
  }

export enum PaymentTypeEnum {
  BASIC_PLAN_MONTHLY = 'BASIC_PLAN_MONTHLY',
  STANDARD_PLAN_MONTHLY = 'STANDARD_PLAN_MONTHLY',
  PROFESSIONAL_PLAN_MONTHLY = 'PROFESSIONAL_PLAN_MONTHLY',
  // @todo 待确认
  SCALE_PLAN_MONTHLY = 'SCALE_PLAN_MONTHLY',
  ENTERPRISE_PLAN_MONTHLY = 'ENTERPRISE_PLAN_MONTHLY',

  BASIC_PLAN_YEARLY = 'BASIC_PLAN_YEARLY',
  STANDARD_PLAN_YEARLY = 'STANDARD_PLAN_YEARLY',
  PROFESSIONAL_PLAN_YEARLY = 'PROFESSIONAL_PLAN_YEARLY',
  // @todo 待确认
  SCALE_PLAN_YEAR = 'SCALE_PLAN_YEAR',
  ENTERPRISE_PLAN_YEARLY = 'ENTERPRISE_PLAN_YEARLY',

  EXTRA_PLAN_LOW = 'EXTRA_PLAN_LOW',
  EXTRA_PLAN_MIDDLE = 'EXTRA_PLAN_MIDDLE',
  EXTRA_PLAN_HIGH = 'EXTRA_PLAN_HIGH',
}
