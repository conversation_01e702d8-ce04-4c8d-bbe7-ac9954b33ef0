import type { ShowMode } from '../store'
import { useBindWinEvent } from '@/hooks'
import cx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { AdditionalContentHeight, titleMap } from '../constants'
import { chatStore } from '../store'
import { iconMap } from './Icons'

interface ContentCardProps {
  title: string
  onClick?: () => void
  disabled?: boolean
  loading?: boolean
  mapKey: ShowMode
  fromColor?: string
  toColor?: string
}

function ContentCard({
  title,
  onClick,
  disabled,
  loading,
  mapKey,
  fromColor = '#fff000',
  toColor = '#00f3ff',
}: ContentCardProps) {
  const [size, setSize] = useState(0)
  const el = useRef<HTMLDivElement>(null)

  useEffect(setLayoutSize, [])
  useBindWinEvent('resize', setLayoutSize)

  function setLayoutSize() {
    const { width, height } = el.current?.getBoundingClientRect() || { width: 0, height: 0 }
    const maxSize = Math.max(width, height)
    setSize(maxSize)
  }

  return (
    <motion.div
      ref={ el }
      onClick={ onClick }
      initial={ { x: 10, opacity: 0 } }
      animate={ { x: 0, opacity: 1 } }
      exit={ { x: -10, opacity: 0 } }
      whileHover={ { y: -5 } }
      className={ cx(
        'rounded-lg relative card-container cursor-pointer h-12',
        'overflow-hidden flex flex-col justify-between shadow-black/5',
        disabled && 'pointer-events-none opacity-40',
        { 'shadow-md border border-gray-200': !loading },
        { 'p-[1px]': loading },
      ) }
    >
      <div className="relative z-1 size-full flex items-center rounded-md bg-white p-2">
        <div className="flex items-center gap-2">
          <div className={ cx(
            'rounded-full p-1',
            { 'bg-gray-700 text-white': loading },
            { 'bg-gray-200 text-gray-400': !loading },
          ) }>
            { iconMap[mapKey] }
          </div>

          <span className={ cx(
            'text-[12px]',
            disabled
              ? 'text-gray-400'
              : 'text-gray-800',
          ) }>
            { title }
          </span>
        </div>
      </div>

      {/* 流光边框 */ }
      <div
        className={ cx(
          'absolute left-1/2 top-1/2 -z-1',
          { 'animate-spin': loading },
        ) }
        style={ {
          backgroundImage: loading
            ? `conic-gradient(${fromColor}, ${toColor}`
            : '',
          width: size * 2,
          height: size * 2,
          translate: '-50% -50%',
          animationDuration: '1.5s',
          animationTimingFunction: 'linear',
        } }
      ></div>
    </motion.div>
  )
}

export default function AdditionalContent() {
  const snap = chatStore.use()
  const items = snap.selectedMode
    .filter(item => item !== snap.showMode)
    .map((item, i) => ({
      title: titleMap()[item],
      key: item,
    }))

  return (
    <div
      className="AdditionalContent-container grid grid-cols-4 gap-2"
      style={ {
        height: AdditionalContentHeight,
      } }
    >
      <AnimatePresence>
        { items.map(item => (
          <ContentCard
            key={ item.key }
            onClick={ () => chatStore.showMode = item.key }
            title={ item.title }
            mapKey={ item.key }
          />
        )) }
      </AnimatePresence>
    </div>
  )
}
