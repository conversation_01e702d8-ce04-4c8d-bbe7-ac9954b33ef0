import type { MutableRefObject } from 'react'

export function useIntersectionObserver<E extends HTMLElement>(
  els: Refs<E>,
  callback: (entry: IntersectionObserverEntry) => void,
  options?: IntersectionObserverInit,
) {
  return useOb(IntersectionObserver, els, callback, options)
}

export function useResizeObserver<E extends HTMLElement>(
  els: Refs<E>,
  callback: (entry: ResizeObserverEntry) => void,
) {
  return useOb(ResizeObserver, els, callback)
}

export function useMutationObserver<E extends HTMLElement>(
  el: MutableRefObject<E | null>,
  callback: (entry: MutationRecord[]) => void,
  options: MutationObserverInit = {
    childList: true,
    subtree: true,
    characterData: true,
  },
) {
  const latestCallback = useRef(callback)

  useEffect(() => {
    latestCallback.current = callback
  }, [callback])

  useEffect(
    () => {
      if (!el.current)
        return

      const ob = new MutationObserver(latestCallback.current)
      ob.observe(el.current, options)

      return () => {
        ob.disconnect()
      }
    },
    [el, options],
  )
}

function useOb<
  C extends new (
    callback: (entries: T[], observer: InstanceType<C>) => void,
    options?: any
  ) => {
    observe: (target: HTMLElement) => void
    unobserve: (target: HTMLElement) => void
    disconnect: () => void
  },
  T,
  E extends HTMLElement,
>(
  ObserverClass: C,
  els: Refs<E>,
  callback: (entry: T) => void,
  options?: ConstructorParameters<C>[1],
) {
  const ob = useRef<InstanceType<C>>()
  const memoizedEls = useMemo(() => els, [...els])
  const latestCallback = useRef(callback)

  useEffect(() => {
    latestCallback.current = callback
  }, [callback])

  useEffect(() => {
    // @ts-ignore
    ob.current = new ObserverClass(
      (entries: T[]) => {
        entries.forEach(latestCallback.current)
      },
      options,
    )

    const elements = memoizedEls.map(el => el.current).filter(Boolean) as E[]
    elements.forEach(el => ob.current?.observe(el))

    return () => {
      ob.current?.disconnect()
    }
  }, [options, memoizedEls, ObserverClass])

  return ob
}

export type Refs<E> = MutableRefObject<(E | null | undefined)>[]
