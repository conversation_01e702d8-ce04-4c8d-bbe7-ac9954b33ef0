import type { ChatP<PERSON>, FileItem } from '../types'
import Welcome from '@/components/RootLayoutV2/components/Welcome'
import { useMemoFn } from '@/hooks'
import cx from 'clsx'
import { chatStore } from '../store'
import { ChatHistory } from './ChatHistory'
import { ChatInput } from './ChatInput'
import ProductVisualizer from './ProductVisualizer'
import { Sidebar } from './Sidebar'
import { Step } from './Step'

export const Chat = memo<ChatProps>(({
  messages,
  onSendMsg,
  onLoadMore,
  setMessages,
  isLoading,
  hasMore,
  className,
}) => {
  const snap = chatStore.use()

  const [needScroll, setNeedScroll] = useState(true)
  const closeScroll = useMemoFn(() => setNeedScroll(false))
  const enableScroll = useMemoFn(() => setNeedScroll(true))

  const hangleSendMsg = useCallback(
    (message: string, files: FileItem[]) => {
      onSendMsg(message, files)
      enableScroll()
    },
    [onSendMsg, enableScroll],
  )

  return <div
    className={ cx(
      'flex flex-col gap-4 backdrop-blur-sm p-4 relative pl-22 w-full',
      { 'justify-center items-center': messages.length === 0 },
      className,
    ) }
  >
    <Sidebar
      disabled={ isLoading }
      setMessages={ setMessages }
    />

    { snap.openWorkflow
      ? <div className={ cx(
          'flex flex-1 overflow-hidden justify-center gap-8',
          !snap.previewMsgs.length && 'mx-auto',
        ) }
        >
          <ProductVisualizer className='grow-0 shrink-0' />

          <div className="min-w-0 flex flex-1 flex-col gap-2">
            <ChatHistory
              messages={ messages }
              onLoadMore={ onLoadMore }
              hasMore={ hasMore }
              className="flex-1 bg-white rounded-2xl shadow-xl"

              needScroll={ needScroll }
              onWheel={ closeScroll }
              setNeedScroll={ setNeedScroll }
            />

            { snap.showStep && <Step />}
          </div>
        </div>

      : <div className="h-full flex flex-1 flex-col items-center justify-center gap-8">
          <ChatInput
            onSendMsg={ hangleSendMsg }
            isLoading={ isLoading }
            className="w-4xl"
          >
            <Welcome className="left-1/2 w-full !absolute -top-30 -translate-x-1/2" />
          </ChatInput>
        </div> }
  </div>
})
