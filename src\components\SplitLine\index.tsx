import clsx from 'clsx'
import { memo } from 'react'

/**
 * 分割线
 */
export const SplitLine = memo((
  {
    style,
    className,
    height = 8,
    innerClassName,
  }: SplitLineProps,
) => {
  return <div
    className={ clsx(
      'flex items-center justify-center w-full',
      className,
    ) }
    style={ {
      height,
      ...style,
    } }
  >
    <div className={ clsx(
      'h-px w-full bg-gray-600 opacity-10',
      innerClassName,
    ) }></div>
  </div>
})

SplitLine.displayName = 'SplitLine'

export type SplitLineProps = {
  className?: string
  innerClassName?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  height?: number
}
