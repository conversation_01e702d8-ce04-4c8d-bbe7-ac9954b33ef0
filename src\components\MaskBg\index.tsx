import type { CSSProperties, HTMLAttributes } from 'react'
import classnames from 'clsx'
import styles from './styles.module.scss'

export function MaskBg({
  style,
  className,
  children,
  ...rest
}: MaskBgProps) {
  return <div
    className={ classnames(
      'absolute inset-0 z-50',
      styles.maskBg,
      className,
    ) }
    style={ style }
    { ...rest }
  >
    { children }
  </div>
}

MaskBg.displayName = 'MaskBg'

export type MaskBgProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode
}
& HTMLAttributes<HTMLDivElement>
