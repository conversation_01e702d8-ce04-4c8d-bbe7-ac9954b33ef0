import type { CSSProperties, ReactNode } from 'react'

import classnames from 'clsx'
import Lottie from 'lottie-web'
import animationData from './photog_logo_loading.json'

function _LogoLoading({
  style,
  className,
  size = '7vw',
}: LogoLoadingProps) {
  const root = useRef<HTMLDivElement>(null)

  useEffect(() => {
    Lottie.loadAnimation({
      container: root.current!,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData,
    })
  }, [])

  return (<div
    ref={ root }
    className={ classnames(className) }
    style={ {
      width: size,
      height: size,
      ...style,
    } }
  >

  </div>)
}
_LogoLoading.displayName = 'LogoLoading'

export const LogoLoading = memo(_LogoLoading)

export type LogoLoadingProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode
  size?: number | string
}
