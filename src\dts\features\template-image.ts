import { TagDO } from "./tag"

export interface TemplateImageDO {
  id: string
  createUserString: null
  createTime: string
  disabled: boolean
  name: string
  url: string
  compressUrl: string
  angle: number
  depressionAngle: number
  focalLength: number
  masks: TemplateImageMaskDO[]
  tags: TagDO[]
  type: 'public'
}

export interface LocalUpImageDO {
  bgUrl: string,
  maskUrl: string,
}

interface TemplateImageMaskDO {
  id: string
  bgResourceId: string
  typeId: string
  type: string
  name: string
  url: string
}

export interface ReplaceState {
  loading: boolean
  materialInfoId: string
  bgLibList: TemplateImageDO[]
  bgLibTotal: number
  size: string
}
