import type { ReactNode } from 'react'
import { clamp } from '@jl-org/tool'

export const PhoneFrame = memo<PhoneFrameProps>(({
  scale = 1,
  children,
  className = '',
  showStatusBar = true,
  showHomeIndicator = true,
}) => {
  const scaledWidth = 375 * scale
  const scaledHeight = 812 * scale
  const notchSize = 20 * scale
  const notchWidth = 100 * scale
  const borderRadius = 60 * scale
  const innerBorderRadius = 50 * scale
  const homeIndicatorWidth = 64 * scale
  const homeIndicatorHeight = 3 * scale
  const phoneBorderSize = clamp(1.5, 8, 8 * scale)

  return (
    <div className={ className }>
      <div
        className="bg-black"
        style={ {
          width: scaledWidth,
          height: scaledHeight,
          padding: phoneBorderSize,
          borderRadius,
        } }>
        <div
          className="relative h-full w-full overflow-hidden bg-white dark:bg-gray-800"
          style={ {
            borderRadius: innerBorderRadius,
          } }>
          { showStatusBar && (
            <div className="flex items-center justify-between px-6 pb-2 pt-3 text-sm text-black font-medium dark:text-white">
              <span>9:33</span>

              <div
                className="absolute left-1/2 top-[12px] transform rounded-full bg-black -translate-x-1/2"
                style={ {
                  height: notchSize,
                  width: notchWidth,
                } }></div>

              <div className="flex items-center gap-1">
                <svg className="ml-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.24 0 1 1 0 01-1.415-1.414 5 5 0 017.07 0 1 1 0 01-1.415 1.414zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>

                <div className="h-3 w-6 border border-black rounded-full p-[1px] dark:border-white">
                  <div className="size-full rounded-full bg-black dark:bg-white"></div>
                </div>
              </div>
            </div>
          ) }

          { children }

          { showHomeIndicator && (
            <div
              className="absolute bottom-2 left-1/2 transform rounded-full bg-black -translate-x-1/2 dark:bg-white"
              style={ {
                height: homeIndicatorHeight,
                width: homeIndicatorWidth,
              } }></div>
          ) }
        </div>
      </div>
    </div>
  )
})

export interface PhoneFrameProps {

  scale?: number

  children: ReactNode

  className?: string

  showStatusBar?: boolean

  showHomeIndicator?: boolean
}
