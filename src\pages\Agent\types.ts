import type { Role } from '@/api/ChatGPTApi'
import type { FileItem as _FileItem } from '@/components/Uploader'
import type { AddTextParams } from '@/utils'
import type { LogEntry } from '@/worker/parseAgent'
import type { Dispatch, SetStateAction } from 'react'
import type { options } from './constants'

export interface Msg {
  id: string
  type: 'thinking' | 'thinkDone' | 'answer' | 'embed' | 'progress' | 'loading'
  mode?: ChatMode
  files: FileItem[]
  content: string
  role: Role
  timestamp: number
  thinkingText?: string
  thinkTitle?: string
  posterLayout?: AddTextParams[]
  children?: React.ReactNode
  logs?: readonly LogEntry[]
}

export type Lang = 'zh' | 'en'

export interface ChatProps {
  messages: Msg[]
  onSendMsg: SendMsg
  onLoadMore: () => void
  isLoading?: boolean
  hasMore?: boolean
  className?: string
  setMessages: Dispatch<SetStateAction<Msg[]>>
}

export interface ChatInputProps {
  onSendMsg: SendMsg
  isLoading?: boolean
  className?: string
  children?: React.ReactNode
}

export interface ChatMessageProps {
  message: Msg
  isLoading?: boolean
  className?: string
}

export type ChatHistoryProps = {
  messages: Msg[]
  onLoadMore: () => void
  isLoading?: boolean
  hasMore?: boolean
  className?: string
  needScroll?: boolean
  setNeedScroll?: (needScroll: boolean) => void
} & React.HTMLAttributes<HTMLDivElement>

type SendMsg = (message: string, files: FileItem[]) => void

export type FileItem = _FileItem & {
  /**
   * 编辑器上画的 url
   */
  rawUrl: string
  file?: File
}

export type ChatMode = 'chat' | typeof options[number]['value']

export type ImgPositon = {
  left: string
  top: string
  desc: string
}

export type ImgDetail = {
  url: string
  title: string
  desc: string
}

export type ChartItem = {
  value: string
  name: string
}

export type MarketResearch = {
  priceDistribution: ChartItem[]
  ageDistribution: ChartItem[]
  regionalDistribution: ChartItem[]
}
