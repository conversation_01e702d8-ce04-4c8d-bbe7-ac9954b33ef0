import { GradientText } from '@/components/GradientText'
import { IS_ZH } from '@/config'
import { useT } from '@/hooks'
import cn from 'clsx'
import { motion } from 'framer-motion'
import React from 'react'

type WelcomeProps = {
  title?: string
  subtitle?: string
  className?: string
}

const Welcome = React.memo(
  ({
    title,
    subtitle,
    className,
  }: WelcomeProps) => {
    const t = useT()

    return (
      <div
        className={ cn('text-center', className) }
      >
        { IS_ZH
          ? <GradientText
              showAnimate={ false }
              colors={ [
                'rgb(47, 161, 238) 0%',
                'rgb(36, 135, 216) 10%',
                '#3278E4 40%',
                '#2962B9 60%',
                '#225199',
              ] }
              className="text-3xl md:text-4xl !font-bold"
              style={ {
                lineHeight: '50px',
              } }
            >
              { title || t('photog.welcome') }
            </GradientText>

          : <motion.h1
              className="text-3xl text-gray-900 font-bold md:text-4xl"
              initial={ { opacity: 0, y: 20 } }
              animate={ { opacity: 1, y: 0 } }
              transition={ { duration: 0.6, delay: 0.2 } }
              style={ {
                lineHeight: '50px',
              } }
            >
              { title || t('photog.welcome') }
            </motion.h1> }

        <motion.p
          className="mx-auto text-base"
          initial={ { opacity: 0 } }
          animate={ { opacity: 1 } }
          transition={ { duration: 0.6, delay: 0.4 } }
          style={ {
            lineHeight: '50px',
          } }
        >
          { subtitle || t('photog.welcome-sub') }
        </motion.p>
      </div>
    )
  },
)

Welcome.displayName = 'Welcome'

export default Welcome
