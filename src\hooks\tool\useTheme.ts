import type { Theme } from '@jl-org/tool'
import { getCurrentTheme, setHTMLTheme, toggleTheme } from '@/utils'
import { onChangeTheme } from '@jl-org/tool'
import { useEffect } from 'react'
import { useMutationObserver } from './ob'

export function useChangeTheme(
  onLight?: VoidFunction,
  onDark?: VoidFunction,
) {
  useEffect(
    () => {
      toggleTheme(getCurrentTheme().theme)
      const unbind = onChangeTheme(
        () => {
          setHTMLTheme('light')
          onLight?.()
        },
        () => {
          setHTMLTheme('dark')
          onDark?.()
        },
      )
      return unbind
    },
    [onDark, onLight],
  )
}

export function useTheme(defaultTheme: Theme = 'light') {
  const [theme, setTheme] = useState(defaultTheme)
  const htmlRef = useRef<HTMLElement>(document.documentElement)

  const _setTheme = useCallback(
    (theme?: Theme) => {
      const nextTheme = toggleTheme(theme)
      setTheme(nextTheme)
    },
    [setTheme],
  )

  useEffect(
    () => {
      const themeInfo = getCurrentTheme()
      setTheme(themeInfo.theme)
      setHTMLTheme(themeInfo.theme)

      const unbindSystemTheme = onChangeTheme(
        () => setTheme('light'),
        () => setTheme('dark'),
      )

      return unbindSystemTheme
    },
    [],
  )

  useMutationObserver(
    htmlRef,
    () => {
      const theme = getCurrentTheme().theme
      setTheme(theme)
    },
    {
      attributes: true,
      attributeFilter: ['class'],
    },
  )

  return [theme, _setTheme] as const
}
