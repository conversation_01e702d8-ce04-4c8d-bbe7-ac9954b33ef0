import type { FileItem, UploaderProps } from '.'
import { god } from '@/god'
import { isValidFileType } from '@/utils'
import { blobToBase64, getImg } from '@jl-org/tool'
import { useRef, useState } from 'react'

export function useGenState(
  {
    maxCount,
    maxSize,
    distinct,
    disabled,
    onChange,
    onExceedCount,
    onExceedSize,
    onExceedPixels,
    accept = '',
    autoClear = false,
    maxPixels,
  }: UploaderProps,
) {
  const [dragActive, setDragActive] = useState(false)
  const [dragInvalid, setDragInvalid] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleFiles = async (fileList: File[], e: any) => {
    const newImages: FileItem[] = []
    const existingFiles = new Set<string>()

    for (const file of fileList) {
      if (!isValidFileType(file, accept)) {
        continue
      }

      if (maxCount && newImages.length >= maxCount) {
        onExceedCount?.()
        if (!onExceedCount) {
          god.messageError('Max count exceeded')
        }
        break
      }

      if (maxSize && file.size > maxSize) {
        onExceedSize?.(file.size)
        if (!onExceedSize) {
          god.messageError('Max size exceeded')
        }
        continue
      }

      if (maxPixels && file.type.startsWith('image/')) {
        const src = URL.createObjectURL(file)
        const img = await getImg(src)
        URL.revokeObjectURL(src)
        if (!img)
          continue

        const { naturalWidth, naturalHeight } = img
        if (naturalWidth > maxPixels.width || naturalHeight > maxPixels.height) {
          onExceedPixels?.(naturalWidth, naturalHeight)
          if (!onExceedPixels) {
            god.messageError('Max pixels exceeded')
          }
          continue
        }
      }

      if (distinct) {
        const fileKey = `${file.name}-${file.size}-${file.type}`
        if (existingFiles.has(fileKey)) {
          continue
        }
        existingFiles.add(fileKey)
      }

      try {
        const base64 = await blobToBase64(file)
        newImages.push({ file, base64 })
      }
      catch (error) {
        console.error('Failed to convert file to base64:', error)
        continue
      }
    }

    if (newImages.length > 0) {
      onChange?.(newImages)
    }

    e.target.value = ''
    if (autoClear) {
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.value = ''
        }
      })
    }
  }

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    if (disabled)
      return
    e.preventDefault()
    e.stopPropagation()

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)

      if (accept && e.dataTransfer.items.length > 0) {
        const files = Array.from(e.dataTransfer.items)
        const hasInvalidFile = files.some((item) => {
          if (item.kind === 'file') {
            const file = item.getAsFile()
            return file && !isValidFileType(file, accept)
          }
          return false
        })
        setDragInvalid(hasInvalidFile)
      }
      else {
        setDragInvalid(false)
      }
    }
    else if (e.type === 'dragleave') {
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.clientX
      const y = e.clientY

      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        setDragActive(false)
        setDragInvalid(false)
      }
    }
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    if (disabled)
      return
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    setDragInvalid(false)

    if (e.dataTransfer.files?.[0]) {
      handleFiles(Array.from(e.dataTransfer.files), e)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled)
      return
    e.preventDefault()

    if (e.target.files?.[0]) {
      handleFiles(Array.from(e.target.files), e)
    }
  }

  const handlePaste = (e: React.ClipboardEvent<HTMLDivElement>) => {
    if (disabled)
      return

    const items = e.clipboardData.items
    if (!items) {
      return
    }

    const fileList: File[] = []
    for (const item of items) {
      if (item.kind === 'file') {
        const file = item.getAsFile()
        file && fileList.push(file)
      }
    }

    fileList.length > 0 && handleFiles(fileList, e)
  }

  return {
    dragActive,
    dragInvalid,
    inputRef,
    handleDrag,
    handleDrop,
    handleChange,
    handlePaste,
  }
}
