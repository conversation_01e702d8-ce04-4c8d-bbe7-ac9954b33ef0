import { Flex, Typography } from "antd"
import Charge from "@/assets/svg/charge.svg"
import classnames from 'clsx'

import SvgIcon from "@/components/SvgIcon"
import styles from "./index.module.less"
import AvatarSVG from "@/assets/svg/avatar.svg"
import { useNavi } from '@/hooks'
import { useSnapshot } from 'valtio'
import { userStore } from '@/store/userStore'

export default memo(function UserAvatar() {

  const to = useNavi()
  const userinfo = useSnapshot(userStore).userinfo
  const { totalCredits = 0 } = userinfo

  function toCharge() {
    to('/pricing')
  }

  return (
    <div className={styles["user-info"]}>
      <div className={styles.avatar}>
        <AvatarSVG />
      </div>

      <Flex vertical className={styles["content"]}>
        <Flex align="center">
          <Typography.Text className={styles["nickname"]}>
            {userinfo.nickname}
          </Typography.Text>
        </Flex>
        <div className={styles["email"]}>{userinfo.email}</div>
      </Flex>

      <Flex className={styles["credit-content"]}>
        <div className={styles["credit"]}
          onClick={toCharge}
          style={{ width: '156px', height: '72px' }}>
          <div className={classnames(styles["credit1"])} style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Charge />
          </div>
          <div className={styles["credit-name"]}>{totalCredits}</div>
        </div>

        <div className={styles["credit"]}
          onClick={toCharge}
          style={{ width: '72px', height: '72px', marginLeft: '15px' }}>
          <div className={styles["credit1"]}>

            <SvgIcon icon="credit3" className={styles["credit-img"]}></SvgIcon>
            <SvgIcon icon="credit2" className={styles["credit-img-right"]}></SvgIcon>
          </div>

        </div>
      </Flex>

    </div>
  )
})