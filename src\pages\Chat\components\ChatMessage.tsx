import type { ChatMessageProps, Msg } from '../types'
import { FakeProgress } from '@/components/Progress'
import { IS_ZH } from '@/config'
import { mdToHTML, typeTxt } from '@/utils'
import { downloadByUrl } from '@jl-org/tool'
import { useAsyncEffect } from 'ahooks'
import cx from 'clsx'
import { motion } from 'framer-motion'
import { Check, Download, Loader } from 'lucide-react'
import { chatStore } from '../store'

const ShowMsg = memo<{
  isUser: boolean
  message: Msg
}>((
  {
    isUser,
    message,
  },
) => {
  if (message.type === 'loading') {
    return <Loader className="animate-spin !animate-duration-[2000ms]" />
  }

  if (isUser) {
    return <UserMsg msg={ message } />
  }

  return <AiMsg msg={ message } />
})

export const ChatMessage = memo<ChatMessageProps>(({ message, className, isLoading }) => {
  const isUser = message.role === 'user'
  const isThinking = message.type === 'thinking'
  const thinkDone = message.type === 'thinkDone'

  return (
    <motion.div
      layout
      className={ cx(
        'flex gap-2 items-start chat-message relative markdown-body',
        isUser
          ? 'flex-row-reverse'
          : 'flex-row',
        { 'thinking !text-gray-400': isThinking || thinkDone },
        { 'text-gray-700': !isThinking || !thinkDone },
        className,
      ) }
    >
      {/* 头像 */ }
      {/* { !isUser && <motion.div
        initial={ { scale: 0 } }
        animate={ { scale: 1 } }
        transition={ { type: 'spring', stiffness: 400, damping: 20 } }
        className={ cx(
          'flex justify-center items-center w-8 h-8 rounded-full border-solid',
          isUser
            ? 'bg-lightBg'
            : 'bg-white',
        ) }
      >
        <SvgIcon icon="logo-photog" />
      </motion.div> } */}

      <div
        style={ {
          border: !(isThinking || thinkDone)
            ? '1px solid #e5e7eb'
            : '',
          backgroundImage: IS_ZH && isUser
            ? 'linear-gradient(90deg, #3366F0 0%, #7550F9 100%)'
            : undefined,
          color: IS_ZH && isUser
            ? '#fff'
            : undefined,
        } }
        className={ cx(
          'max-w-[90%] rounded-2xl relative',
          { 'py-4': !isThinking && !thinkDone },
          (isThinking || thinkDone)
            ? ''
            : isUser
              ? 'bg-lightBg rounded-tr-none'
              : 'bg-white rounded-tl-none',
        ) }
      >
        {/* <Typewriter completed={ message.completed }> */ }
        <ShowMsg message={ message } isUser={ isUser } />
        {/* </Typewriter> */ }
      </div>
    </motion.div>
  )
})

const UserMsg = memo(({ msg }: { msg: Msg }) => {
  return <div>
    {
      msg.type === 'embed'
        ? <div dangerouslySetInnerHTML={ { __html: msg.content } }></div>
        : <div>{ msg.content }</div>
    }

    <div className="flex flex-wrap gap-4">
      { msg.files?.[0]?.base64 && msg.files.map((item, index) =>
        <div className="mt-4 rounded-xl bg-white" key={ index }>
          <img
            src={ item.base64 }
            className="w-72 rounded-md object-cover"
          />
        </div>,
      ) }
    </div>
  </div>
})

const AiMsg = memo(({ msg }: { msg: Msg }) => {
  if (msg.children)
    return msg.children

  const [displayHtml, setDisplayHtml] = useState('')
  const isThinking = msg.type === 'thinking'
  const thinkDone = msg.type === 'thinkDone'
  const hasThinkTitle = ((isThinking || thinkDone) && msg.thinkTitle)

  const snap = chatStore.use()

  /** 记录上一次打字的位置 */
  const lastIndexRef = useRef(0)
  /** 记录当前的停止函数 */
  const stopTypingRef = useRef<(() => void) | null>(null)
  /** 记录上一次的内容 */
  const lastContentRef = useRef('')

  useAsyncEffect(
    async () => {
      if (msg.type === 'embed') {
        setDisplayHtml(msg.content)
        return
      }

      if (snap.fromHistory || msg.completed) {
        const html = await mdToHTML(msg.content)
        setDisplayHtml(html)
        return
      }

      /** 如果内容变化，处理打字效果 */
      if (msg.content !== lastContentRef.current) {
        /** 如果有正在进行的打字效果，先停止 */
        if (stopTypingRef.current) {
          stopTypingRef.current()
          stopTypingRef.current = null
        }

        /**
         * 确定继续的位置
         * 如果新内容包含旧内容的开头部分，则从上次位置继续
         */
        const continueFromIndex = msg.content.startsWith(lastContentRef.current)
          ? lastIndexRef.current
          : 0

        /** 启动打字效果 */
        const { stop } = typeTxt({
          content: msg.content,
          continueFromIndex,
          callback: async (text) => {
            const md = await mdToHTML(text)
            setDisplayHtml(md)
            lastIndexRef.current = text.length
          },
        })

        /** 保存停止函数和当前内容 */
        stopTypingRef.current = stop
        lastContentRef.current = msg.content
      }
    },
    [msg.content, msg.type, snap.fromHistory, msg.completed],
  )

  /** 组件卸载时清理 */
  useEffect(() => {
    return () => {
      if (stopTypingRef.current) {
        stopTypingRef.current()
      }
    }
  }, [])

  return <div className="relative h-full">
    {/* 深度思考 */ }
    { (isThinking || thinkDone) && <div
      style={ {
        border: '1px solid #e5e7eb',
      } }
      className={ cx(
        `flex items-center gap-2 relative -translate-x-6 mb-4 w-fit px-2 pr-3`,
        'bg-lightBg py-1 px-4 flex justify-center items-center gap-2 rounded-lg',
      ) }
    >
      <img src={ new URL('@/pages/Chat/assets/deepThink.svg', import.meta.url).href } alt="" width={ 18 } />
      <div className={ cx(
        'text-black !text-[13px] text-left',
        { loadingText: isThinking },
      ) }
      >
        {
          isThinking
            ? msg.thinkingText || 'Deep thinking'
            : msg.thinkingText || 'Thinking done'
        }
      </div>
    </div> }

    <div className={ cx(
      'relative pl-3',
      { 'pt-10': hasThinkTitle },
    ) }>
      { hasThinkTitle && <div className="absolute left-3 top-0 whitespace-nowrap text-sm text-gray-800">
        { msg.thinkTitle }
      </div> }
      <div dangerouslySetInnerHTML={ { __html: displayHtml } } />

      {/* 侧边 Line */ }
      {
        ((isThinking || thinkDone) && msg.content) && <>
          <div className="absolute top-1 h-full w-[1px] bg-black/10 -left-3">
            {/* Check Icon */ }
            <div className={ cx(
              'absolute size-5 flex items-center justify-center rounded-full -left-[0.6rem] -top-1',
              thinkDone
                ? 'bg-primary'
                : 'bg-gray-300',
            ) }>
              { thinkDone
                ? <Check
                    size={ 13 }
                    strokeWidth={ 4 }
                    color="#fff"
                  />
                : <Loader
                    className="animate-spin"
                    size={ 14 }
                    color="#fff"
                  /> }
            </div>
          </div>

          {/* 底部 Line */ }
          {/* <div className="absolute h-[1px] w-full bg-gray-200 -bottom-12 -left-3"></div> */ }
        </>
      }
    </div>

    {/* 加载 */ }
    { msg.type === 'progress' && <div
      className="relative mt-4 h-40 w-64"
    >
      <FakeProgress size={ 100 } showText={ false } />
    </div> }

    {/* 图片 */ }
    { msg.files?.[0]?.base64 && <div className="group relative mt-4 flex flex-wrap gap-4">
      { msg.files.map((item, index) =>
        <div className="rounded-xl bg-white" key={ index }>
          <img
            src={ item.base64 }
            className="w-72 rounded-md object-cover"
          />
        </div>,
      ) }

      <div className="absolute right-10 top-1 cursor-pointer rounded-full bg-innerBg p-2 transition-all duration-300 hover:opacity-50">
        <Download
          size={ 16 }
          color="#888"
          strokeWidth={ 1 }
          onClick={ () => {
            downloadByUrl(msg.files[0].base64)
          } }
        />
      </div>

      <div className="absolute right-10 top-1 cursor-pointer rounded-full bg-innerBg p-2 transition-all duration-300 hover:opacity-50">
        <Download
          size={ 16 }
          color="#888"
          strokeWidth={ 1 }
          onClick={ () => {
            downloadByUrl(msg.files[0].base64)
          } }
        />
      </div>

      <div className="absolute right-1 top-1 cursor-pointer rounded-full bg-innerBg p-2 transition-all duration-300 hover:opacity-50">
        <img
          src={ new URL('@/assets/svg/edit2.svg', import.meta.url).href }
          onClick={ () => {
            chatStore.showEditor = true
            chatStore.selectedMsg = msg
          } }
        />
      </div>
    </div> }
  </div>
})
