import type { HistoryItem } from '@/pages/Photoe/types'
import type { <PERSON><PERSON>uery, PagerList } from '../view'
import type { CatalogDO } from './catalog'
import { TreeDataNode } from 'antd'

export interface MaterialState {
  /** 列表加载loading */
  loading: boolean
  /** 当前加载的素材列表 */
  materials: PagerList<MaterialDO>
  /** 操作素材loading */
  actionLoading: boolean
  /** 素材列表query params */
  queryParams: Omit<ModelsQueryPO, 'directoryId'>
  /** 当前使用的模型 */
  currentModel?: MaterialDO
  LocalUpModalOpen: boolean
  AiDrawModalOpen: boolean
  LocalUpImgs: HistoryItem[]
  AiDrawingLoad: boolean
}

/** 素材对象 */
export interface MaterialDO {
  /** id */
  id: string
  /** 名称 */
  name: string
  /** 封面原图 */
  coverImage: string
  /** 封面图-经过自动抠图 */
  coverImageRemoveBackground: string
  /** 创建时间 */
  creationTime: string
  /** 创建人 */
  createUserString: string
  /** 状态 */
  status: TrainTaskStatusEnum
  /** 品牌 */
  brand: string
  /** 设计师 */
  designer: string
  /** 价格 */
  price: number
  /** 类型name */
  type: ModelTypeDO['name']
  /** 类型id */
  typeId: ModelTypeDO['id']

  /** 模型 url */
  objModelUrl: string

  /** queryTypeImgUrl 1 则有 0 则无 */
  typeImgUrl?: ModelTypeDO['imgPath']
}

export interface ModelTypeDO {
  /** ID */
  id: string
  /** 创建时间 */
  createTime: string
  /** 目录名 */
  name: string
  /** 父级id 根目录则为 0 */
  parentTypeId: string
  /** 缩略图 */
  imgPath: string
  numOfChildren: number
  numOfSubType: number
}

export type ModelTypeVO = {
  children: ModelTypeVO[]
  id: string
  createTime: string
  name: string
  parentTypeId: number
  numOfChildren: number
  numOfSubType: number
  imgPath: string
  key: string
}

export enum ModelQueryTypeEnum {
  /** 我的素材 */
  MY_ASSETS = -2,
  /** samples */
  SAMPLES = -1,
}

// 创建训练任务参数
export interface LoraTrainTaskPO {
  /** 封面图 */
  coverImage: string | null
  /** 训练图 */
  multiAngleImages: string[] | null
  /** 名称 */
  name: string
  /** 品牌 */
  brand: string
  /** 设计师 */
  designer: string
  /** 价格 */
  price: number
  /** 目录id */
  directoryId: string
  /** 类型 */
  typeId: ModelTypeDO['id']
  /** 1:private 2:public */
  viewArea: 1 | 2
}

export interface ModelsQueryPO extends PageQuery {
  /** 所属文件夹id，不传就是所有 */
  directoryId?: CatalogDO['id']
  /** name过滤 */
  name?: string
  /** lora 训练状态 */
  status?: string
  createUser?: string
  /** 物料类型 id */
  typeId?: ModelTypeDO['id']
  /** 是否包含子类型 */
  isIncludingSubType?: boolean
  /** 是否查询物料类型图片 示例值(1-是，0-否) */
  queryTypeImgUrl?: 1 | 0
  /**
   * 料模型查询范围,示例值
   * - (1-私人，2-公共，3-当前用户，4-当前用户和公共部分, 5-所有, 6-指定)
   */
  queryViewAreaRange?: ModelQueryViewEnum
  /**
   * 物料模型可见度,示例值(1-私人，2-公共)
   */
  viewArea?: 1 | 2
}

export enum ModelQueryViewEnum {
  /** 所有 */
  ALL = 1,
  /** 公共部分 */
  PUBLIC,
  /** 当前用户 */
  CURRENT_USER,
  /** 公共部分+当前用户 */
  CURRENT_USER_AND_PUBLIC,
  /** 指定 */
  SPECIFIC,
}

export enum TrainTaskStatusEnum {
  /** 取消 */
  CANCELED = -3,
  /** 失败 */
  ERROR,
  /** 排队中 */
  QUEUE,
  /** 准备 */
  PREPARE,
  /** 训练中 */
  PENDING,
  /** 完成 */
  FINISHED,
}

export interface CustomTrainTaskPO {
  frameScale: string
  prompt: string
  style: string
  scene: string
  type: string | null
}

export interface PromtTrainTaskPO {
  frameScale: string
  prompt: string
  type: string | null
}
