import type { SidebarProps } from '@/components/Sidebar'
import type { Dispatch, SetStateAction } from 'react'
import type { ShowMode } from '../store'
import type { Msg } from '../types'
import { ChatApi, SessionType } from '@/api/ChatApi'
import { Role } from '@/api/ChatGPTApi'
import { Sidebar as S } from '@/components/Sidebar'
import { RomanNumMap } from '@/config'
import { god } from '@/god'
import { genArr, timeGap } from '@jl-org/tool'
import cx from 'clsx'
import { ChatEventBus, thinkTitleMap } from '../constants'
import { chatStore, resetChatStore } from '../store'
import { genMsg } from '../tool'

const InnerSidebar = forwardRef<SidebarRef, Props>((
  {
    disabled,
    setMessages,
  },
  ref,
) => {
  const page = useRef(1)

  const [selectedId, setSelectedId] = useState('')
  const [loading, setLoading] = useState(false)
  const [items, setItems] = useState<(SidebarProps['data'][0] & { chatId: string })[]>([])
  const [hasMore, setHasMore] = useState(true)

  function reload() {
    page.current = 1
    setItems([])
    setHasMore(true)
    loadMore()
  }

  useImperativeHandle(ref, () => ({
    reload,
  }))

  useEffect(
    () => {
      ChatEventBus.on('reloadSidebar', reload)
      return () => ChatEventBus.off('reloadSidebar', reload)
    },
    [],
  )

  const loadMore = useCallback(
    async () => {
      const data = await ChatApi.querySession({
        page: page.current++,
        size: 10,
        sort: 'createTime,desc',
      })

      setItems((prev) => {
        setHasMore(
          (data.total > prev.length + data.list.length)
          && (prev.length + data.list.length) < 100,
        )

        return [...prev, ...data.list.map(item => ({
          id: item.id,
          chatId: item.chatId,
          img: item.originalImageUrls?.[0] || '',
          title: item.content,
          subtitle: '',
          timestamp: timeGap(new Date(new Date(item.createTime).getTime() + 500)) || '',
        }))]
      })
    },
    [],
  )

  const onItemClick = async (id: string) => {
    if (loading)
      return

    const chatId = items.find(item => item.id === id)?.chatId
    if (!chatId) {
      god.messageWarn('Not Found ID')
      return
    }

    reset()
    setSelectedId(id)
    setLoading(true)
    chatStore.loading = true
    chatStore.fromHistory = true

    const data = await ChatApi.querySessionByChatId({
      chatId,
      page: 1,
      size: 999,
      sort: 'createTime,asc',
    })
      .finally(() => {
        setLoading(false)
        chatStore.loading = false
      })

    if (data.list.length <= 0) {
      god.messageWarn('No Data')
      setLoading(false)
      chatStore.loading = false
      return
    }

    let romanNum = 0
    chatStore.chatId = chatId
    chatStore.selectedMode = []

    const getThinkTop = (showMode: ShowMode) => {
      // @ts-ignore
      const num = RomanNumMap[++romanNum] || ''
      const data = thinkTitleMap()[showMode]
      return {
        thinkTitle: `Part ${num}: ${data.title}`,
        thinkingText: `${data.part}`,
      }
    }

    const findType = (type: SessionType) => data.list
      .filter(item => item.type === type)

    const chat = findType(SessionType.Chat)
    const copyWrite = findType(SessionType.Copyright)
    const report = findType(SessionType.Report)
    const videoUrl = findType(SessionType.Video)
    const modelUrl = findType(SessionType.Model)
    const imgUrl = findType(SessionType.Image)

    chat.forEach((item) => {
      if (item.reasoningContent) {
        addThink(item.reasoningContent)
      }
    })

    copyWrite.some(item => !!item.content) && chatStore.selectedMode.push('copyWrite')
    report.some(item => !!item.content) && chatStore.selectedMode.push('report')
    imgUrl.some(item => item.originalImageUrls?.length) && chatStore.selectedMode.push('img')
    videoUrl.some(item => item.videoUrls?.length) && chatStore.selectedMode.push('video')
    modelUrl.some(item => item.modelUrls?.length) && chatStore.selectedMode.push('3dModel')

    if (chatStore.selectedMode.length === 0) {
      god.messageWarn('No Data')
      setLoading(false)
      return
    }

    chatStore.openWorkflow = true

    copyWrite.forEach((item) => {
      if (item.content) {
        chatStore.copyWrite = item.content
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('copyWrite'))
      }
    })

    report.forEach((item) => {
      if (item.content) {
        chatStore.report = item.content
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('report'))
      }
    })

    videoUrl.forEach((item) => {
      if (item.videoUrls?.length > 0) {
        chatStore.videoUrl = item.videoUrls[0]
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('video'))
      }
    })

    modelUrl.forEach((item) => {
      if (item.modelUrls?.length > 0) {
        chatStore.modelUrl = item.modelUrls[0]
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('3dModel'))
      }
    })

    chatStore.previewMsgs = genArr<Msg>(imgUrl.length, () => genMsg())
    imgUrl.forEach((item, index) => {
      if (!item.originalImageUrls?.length)
        return

      const oriUrl = item.originalImageUrls[0]
      try {
        const posterLayout = JSON.parse(item.layout) || {}
        const msg: Msg = {
          ...chatStore.previewMsgs[index],
          files: [
            {
              base64: item.composeImageUrls?.[0] || oriUrl,
              rawUrl: oriUrl,
              file: new File([], ''),
            },
          ],
          posterLayout,
        }

        chatStore.selectedMsg = msg
        chatStore.previewMsgs[index] = msg
      }
      catch (error) {
        console.log(error)
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('img'))
      }
    })

    function addThink(
      think: string,
      thinkData: {
        thinkTitle?: string
        thinkingText?: string
      } = {},
    ) {
      setMessages((prev) => {
        return [
          ...prev,
          genMsg({
            content: think,
            role: Role.Assistant,
            type: 'thinkDone',
            ...thinkData,
          }),
        ]
      })
    }
  }

  function reset() {
    resetChatStore()
    setMessages([])
  }

  return <S
    className={ cx(
      'SidebarContainer absolute left-4 top-1/2 z-50 bg-white -translate-y-1/2 h-96',
      { '!cursor-wait': loading },
    ) }
    disableHeader={ disabled }
    disableItem={ disabled }

    data={ items }
    hasMore={ hasMore }
    loadMore={ loadMore }
    onAddClick={ reset }
    onItemClick={ onItemClick }
  >

  </S>
})

InnerSidebar.displayName = 'Sidebar'
export const Sidebar = memo(InnerSidebar) as typeof InnerSidebar

type Props = {
  disabled?: boolean
  setMessages: Dispatch<SetStateAction<Msg[]>>
}

export type SidebarRef = {
  reload: () => void
}
