import { Skeleton, SkeletonProps } from "antd";
import type { FC, PropsWithChildren } from "react";

import styles from "./index.module.less";

type Props = PropsWithChildren & SkeletonProps;

const RouteLoading: FC<Props> = ({ children, ...skeletonProps }) => {
  return (
    <Skeleton
      active
      className={styles["skeleton"]}
      paragraph={{ rows: 5 }}
      {...skeletonProps}
    >
      {children}
    </Skeleton>
  );
};

export default memo(RouteLoading);
