import SvgIcon from "@/components/SvgIcon"
import { useNavi } from '@/hooks'
import { userStore } from '@/store/userStore'
import { Button } from "antd"
import { FC, useCallback } from "react"
import { useSnapshot } from 'valtio'

const PricingBtn: FC = () => {
  const to = useNavi()
  const totalCredits = useSnapshot(userStore).userinfo.totalCredits

  const onClick = useCallback(() => {
    to("/pricing")
  }, [to])

  return (
    <Button
      type="default"
      icon={<SvgIcon icon="pricing" noFill />}
      style={{ backgroundColor: "#fff" }}
      onClick={onClick}
    >
      {totalCredits}
    </Button>
  )
}

export default memo(PricingBtn)
