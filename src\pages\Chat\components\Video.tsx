import { FakeProgress } from '@/components/Progress'
import cx from 'clsx'
import { motion } from 'framer-motion'
import { memo } from 'react'
import { chatStore } from '../store'

export const Video = memo<VideoProps>((
  {
    style,
    className,
  },
) => {
  const snap = chatStore.use()
  return <motion.div
    initial={ { opacity: 0 } }
    animate={ { opacity: 1 } }
    exit={ { opacity: 0 } }
    transition={ { duration: 0.3 } }
    className={ cx(
      'VideoContainer h-full',
      className,
    ) }
    style={ style }
  >
    <div className="h-full flex flex-col">
      { !snap.videoUrl
        ? <div className="relative my-auto aspect-square min-h-96">
            <FakeProgress showText={ false } />
          </div>

        : <video
            className="size-full object-contain"
            src={ snap.videoUrl }
            autoPlay
            loop
            muted
            controls
            my-auto
          ></video> }
    </div>
  </motion.div>
})

Video.displayName = 'Video'

export type VideoProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
