import { memo } from 'react'
import cn from 'clsx'
import { Badge, Popover } from 'antd'
import { SplitLine } from '@/components/SplitLine'
import { padding } from '@/styles/variable'
import { MessageCircleMore } from 'lucide-react'


const Msg = <div
  className='rounded-full bg-blue-600 text-white size-[30px] flex items-center justify-center flex-shrink-0 cursor-pointer'
>
  <MessageCircleMore />
</div>

const Content = () => {
  const height = 300,
    width = 300,
    headerHeight = 40

  const contentHeight = 80,
    contentTitleHeight = 30

  const msgArr = [
    {
      title: 'Dear users:',
      content: `We sincerely apologize for the occasional generation failures experienced yesterday due to an unexpectedly high number of users accessing our platform after launch.
        We are actively working on optimizing the system to better handle the increased demand. Thank you for your patience and support as we strive to provide a better experience for everyone!`,
      time: '2024-11-27'
    },
  ]

  return <div className=''
    style={{
      height,
      width,
    }}
  >
    <div
      style={{
        height: headerHeight
      }}
      className='flex flex-col justify-between'
    >
      <span className='bg-blue-600 text-white py-1 px-2 rounded-md w-fit'>
        Message list
      </span>

      <SplitLine height={2} />
    </div>

    <div
      style={{
        height: `calc(100% - ${headerHeight + padding}px)`,
        marginTop: padding
      }}
      className='overflow-y-auto flex flex-col gap-2'
    >
      {msgArr.map((item, index) => <div key={index}
        className='flex flex-col'
      >
        <div className='flex gap-2 flex-col w-full'
          style={{
            minHeight: contentHeight
          }}
        >
          {/* 标题 */}
          <div className='flex gap-2 w-full'
            style={{
              height: contentTitleHeight
            }}
          >
            {Msg}

            <div className='flex justify-between w-full'>
              <span className='font-bold'>{item.title}</span>
              <span className='text-light'>{item.time}</span>
            </div>
          </div>

          {/* 内容 */}
          <div className='text-light pl-[2.4rem] w-full'
            style={{
              height: `calc(100% - ${contentTitleHeight}px)`,
              // overflow: 'hidden',
              // display: '-webkit-box',
              // WebkitLineClamp: 2,
              // WebkitBoxOrient: 'vertical',
              // wordBreak: 'break-word'
            }}
          >
            {item.content}
          </div>
        </div>
      </div>)}
    </div>
  </div>
}

export const Info = memo((
  {
    style,
    className,
  }: InfoProps
) => {

  return <div
    className={cn(className)}
    style={style}
  >
    <Popover
      placement='leftBottom'
      content={<Content />}
    >
      <Badge count={1} size={'small'}>
        {Msg}
      </Badge>
    </Popover>
  </div>
})

Info.displayName = 'Info'

export type InfoProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
