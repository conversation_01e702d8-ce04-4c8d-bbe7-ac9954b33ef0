import { LoraTrainTaskPO, type ModelTypeDO } from "./material"

export interface FormTrainTaskPO {
  name: string
  brand: string
  designer: string
  price: number
  folderId: string
  unit: string
  type: string
}
export interface ReferencePO {
  images: {
    name: string
    url: string
  }[]
}
export interface FolderPO {
  createTime: string
  createUserString: string
  id: string
  name: string
  numOfChildren: number
  numOfSubDir: number
  parentDirectoryId: string
}

export interface TrainingPO {
  similarImages: {
    base64: string
    name: string
  }[]
}
export interface formTrainPO {
  name: string
  type: string
  brand: string
  designer: string
  price: string
  folderId: string
}

/**
 * 未知：1 
 * 3d模型：2 
 * 多角度图：3 
 * 视频：4
 */
export enum TaskType {
  Model = 2,
  MultiImgParams,
  Video
}

export interface StartModelPO {
  /** 文件夹路径（含唯一标识）/模型名 */
  threeDimensionModel: string | null
  /** 文件夹路径（含唯一标识）/索引名 */
  materialIndex: string | null
  textureMapping: string[] | null
  taskType: TaskType

  /** 封面图 */
  coverImage: string | null
  /** 训练图 */
  blend: string | null
  /** 名称 */
  name: string
  /** 品牌 */
  brand: string
  /** 设计师 */
  designer: string
  /** 价格 */
  price: number
  /** 目录id */
  directoryId?: string
  /** 类型 */
  typeId: ModelTypeDO["id"]
  /** 1:private 2:public */
  viewArea: 1 | 2
}

export interface GenerateImageParamPO {
  /** 比例底图 */
  underMaskUrl?: string
  /** 物料id */
  materialInfoId: string
  /** ai drawing模板 */
  aiDrawingBgResources: ({
    bgUrl: string
    maskUrl: string

    scene?: string
    style?: string
  } & AdvancedState)[]

  /** 用户上传模板 */
  uploadBgResources: ({
    bgUrl: string
    maskUrl: string
  } & AdvancedState)[]

  /** library模板 */
  libraryBgResources: ({
    name: string
    bgUrl: string
    maskUrl: string
  } & AdvancedState)[]

  /** 重新生图模板 */
  reRequestBgResources: ({
    name: string
    bgUrl: string
    maskUrl: string
  } & AdvancedState)[]
}

type AdvancedState = {
  /** 旋转角 */
  angle?: number
  /** 缩放 */
  scale?: number

  /** 俯仰角 */
  depressionAngle?: number
  /** 透视 */
  focalLength?: number
  /** 偏移dx */
  offsetX?: number
  /** 偏移dy */
  offsetY?: number
  /** 调光系数 */
  lightDimmer?: number
  /** 
   * 是否自动处理 mask
   * ### 如果只有模板库的图，且没有x、y轴、缩放三个数值的调整，传 false，其余情况传 true
   */
  handleMask?: boolean
}