import { memo, type CSSProperties, ReactNode } from 'react'
import styles from './styles.module.scss'
import classnames from 'clsx'


function _LineText({
  style,
  className,
  children,
  needDot,
}: LineTextProps) {

  return (<div
    className={classnames(styles.title, className)}
    style={style}
  >
    {children}

    <div className={styles["line"]}></div>
    <div className={styles["line2"]}></div>

    {
      needDot && (<>
        <div className={styles["dot"]}></div>
        <div className={styles["dot2"]}></div>
      </>
      )
    }

  </div>)
}
_LineText.displayName = 'LineText'

export const LineText = memo(_LineText)

export type LineTextProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode
  /** 添加小点 */
  needDot?: boolean
}