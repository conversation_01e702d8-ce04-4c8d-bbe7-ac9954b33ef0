import type { LogEntry, SearchResult } from '@/worker/parseAgent'
import { Skeleton } from 'antd'
import clsx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { memo } from 'react'
import { chatStore } from '../store'

interface AgentDataViewerProps {
  searchResults: readonly SearchResult[]
  className?: string
  noData?: boolean
}

function AgentDataViewer({ searchResults, className, noData }: AgentDataViewerProps) {
  return (
    <div className={ clsx('AgentDataViewer flex flex-col gap-4 overflow-auto', className) }>
      {/* 搜索结果部分 */ }
      { searchResults.length > 0
        ? (
          <motion.div
            initial={ { opacity: 0, y: 20 } }
            animate={ { opacity: 1, y: 0 } }
            transition={ { duration: 0.5 } }
            className="search-results rounded-lg bg-gray-50 p-4"
          >
            <h3 className="mb-3 text-lg font-medium">搜索结果</h3>
            <div className="flex flex-col gap-3">
              <AnimatePresence initial={ false }>
                { searchResults.map((result, index) => (
                  <motion.div
                    key={ index }
                    style={ {
                      transformOrigin: 'top', // 从顶部开始动画
                    } }
                    /** 定义入场动画 */
                    initial={ { opacity: 0, y: 10, scaleY: 0 } } // 初始状态：透明，向下偏移10px
                    animate={ { opacity: 1, y: 0, scaleY: 1 } } // 动画目标：完全不透明，回到原位
                    transition={ { duration: 0.5, ease: 'easeOut' } } // 动画效果
                    layout // 添加 layout 属性可以在元素增删时平滑调整布局（可选）
                    className="rounded-md bg-white p-3 shadow-sm"
                  >
                    <motion.h4
                      initial={ { opacity: 0 } }
                      animate={ { opacity: 1 } }
                      transition={ { delay: 0.2 + 0.1 * index } }
                      className="mb-1 text-base text-blue-600 font-medium"
                    >
                      <a href={ result.url } target="_blank" rel="noopener noreferrer">
                        { result.title }
                      </a>
                    </motion.h4>
                    <motion.div
                      initial={ { opacity: 0 } }
                      animate={ { opacity: 1 } }
                      transition={ { delay: 0.3 + 0.1 * index } }
                      className="mb-1 truncate text-xs text-gray-500"
                    >
                      { result.url }
                    </motion.div>
                    <motion.p
                      initial={ { opacity: 0 } }
                      animate={ { opacity: 1 } }
                      transition={ { delay: 0.4 + 0.1 * index } }
                      className="overflow-hidden text-sm text-gray-700"
                    >
                      { result.description }
                    </motion.p>
                  </motion.div>
                )) }
              </AnimatePresence>
            </div>
          </motion.div>
        )
        : (
          noData
            ? (
              <motion.div
                initial={ { opacity: 0 } }
                animate={ { opacity: 1 } }
                transition={ { duration: 0.5 } }
                className="h-full flex items-center justify-center"
              >
                <div className="text-center text-gray-400">
                  <motion.div
                    initial={ { scale: 0.5 } }
                    animate={ { scale: 1 } }
                    transition={ {
                      type: 'spring',
                      stiffness: 260,
                      damping: 20,
                    } }
                    className="mb-2 text-4xl"
                  >
                    🔍
                  </motion.div>
                  <motion.div
                    initial={ { opacity: 0 } }
                    animate={ { opacity: 1 } }
                    transition={ { delay: 0.3 } }
                  >
                    No Search Results
                  </motion.div>
                </div>
              </motion.div>
            )
            : (
              <div className="relative h-96 overflow-hidden">
                <div className="loadingText mb-8 text-gray-400">Deep Searching</div>
                <Skeleton active />
              </div>
            )
        ) }

      {/* 无数据展示 */ }
      { searchResults.length === 0 && (
        noData
          ? (
            <motion.div
              initial={ { opacity: 0 } }
              animate={ { opacity: 1 } }
              transition={ { duration: 0.5 } }
              className="h-full flex items-center justify-center"
            >
              <div className="text-center text-gray-400">
                <motion.div
                  initial={ { scale: 0.5 } }
                  animate={ { scale: 1 } }
                  transition={ {
                    type: 'spring',
                    stiffness: 260,
                    damping: 20,
                  } }
                  className="mb-2 text-4xl"
                >
                  📊
                </motion.div>
                <motion.div
                  initial={ { opacity: 0 } }
                  animate={ { opacity: 1 } }
                  transition={ { delay: 0.3 } }
                >
                  No Agent Data
                </motion.div>
              </div>
            </motion.div>
          )
          : (
            <div className="relative h-96 overflow-hidden">
              <Skeleton active />
            </div>
          )
      )
      }
    </div >
  )
}

export default memo(AgentDataViewer)
