import { mdToHTML } from '@/utils'
import { useAsyncEffect } from 'ahooks'
import { Skeleton } from 'antd'
import clsx from 'clsx'
import { motion } from 'framer-motion'

export default function TextContent({ txt, className }: TextContentProps) {
  const [md, setMd] = useState('')
  useAsyncEffect(
    async () => {
      const md = await mdToHTML(txt)
      setMd(md)
    },
    [txt],
  )

  return (
    <motion.div
      initial={ { opacity: 0 } }
      animate={ { opacity: 1 } }
      exit={ { opacity: 0 } }
      transition={ { duration: 0.3 } }
      className={ clsx('text-content-container h-full animate-fade-in-right', className) }
      style={ {
        animationTimingFunction: 'ease-in-out',
        animationDuration: '.6s',
      } }
    >
      <div className="markdown-body h-full flex flex-col overflow-auto space-y-6">
        { !md && <div className="relative h-96 overflow-hidden">
          <div className="loadingText mb-8 text-gray-400">Deep Searching</div>
          <Skeleton active />
          <Skeleton active />
        </div> }

        <div dangerouslySetInnerHTML={ { __html: md } } className="h-full overflow-auto"></div>
      </div>
    </motion.div>
  )
}

TextContent.displayName = 'TextContent'

export type TextContentProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  txt: string
}
