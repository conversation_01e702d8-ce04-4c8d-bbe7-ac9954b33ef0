import { API_MATERIAL_TYPE } from "@/api/material-type"
import { ModelTypeVO } from "@/dts"
import { arrToTree } from '@jl-org/tool'
import { useCallback, useState } from "react"


/**
 * Items 的左侧分类数据
 * @returns 
 */
export function useModelType() {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<ModelTypeVO[]>([])
  const [curItem, setCurItem] = useState<ModelTypeVO>()

  const fetchData = useCallback(
    async (params?: Parameters<typeof API_MATERIAL_TYPE.getList>[0]) => {
      setLoading(true)
      await API_MATERIAL_TYPE.getList(params)
        .then((ret) => {
          const newArr = ret
            .sort(function (a, b) {
              return (a.name + '').localeCompare(b.name + '')
            })
            .map((item) => ({
              ...item,
              pid: item.parentTypeId,
              key: item.id,
            }))

          const data = arrToTree(newArr) as any[]
          setData(data)
          setCurItem(data[0])
        })
        .catch(() => {
          setData([])
        })
      setLoading(false)
    },
    [],
  )

  const selectCurItem = useCallback(
    (id: string) => {
      const item = data.find(item => item.id === id)
      if (item) {
        setCurItem(item)
      }
    },
    [setCurItem, data]
  )

  return {
    fetchData, selectCurItem,
    loading, data, curItem
  }
}
