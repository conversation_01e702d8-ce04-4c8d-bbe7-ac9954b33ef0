import ZipWorker from '@/worker/zipWorker?worker'
import { useCallback, useRef } from 'react'

export function useUnzip(options?: {
  onSuccess?: (files: UnzippedFile[]) => void
  onProgress?: (progress: number) => void
  onError?: (error: string) => void
}) {
  const workerRef = useRef<Worker | null>(null)

  const unzipFile = useCallback((arrayBuffer: ArrayBuffer) => {
    /** 如果已有worker实例则销毁 */
    if (workerRef.current) {
      workerRef.current.terminate()
    }

    /** 创建新的worker实例 */
    workerRef.current = new ZipWorker()

    /** 监听worker消息 */
    workerRef.current.onmessage = (e) => {
      const { type, data } = e.data

      switch (type) {
        case 'result':
          options?.onSuccess?.(data)
          break
        case 'progress':
          options?.onProgress?.(data)
          break
        case 'error':
          options?.onError?.(data)
          break
      }
    }

    /** 发送解压指令 */
    workerRef.current.postMessage({
      type: 'unzip',
      arrayBuffer,
    })
  }, [])

  return {
    unzipFile,
  }
}

export interface UnzippedFile {
  name: string
  data: string
  isText: boolean
  isImage?: boolean
  mimeType?: string
  originalSize: number
}
