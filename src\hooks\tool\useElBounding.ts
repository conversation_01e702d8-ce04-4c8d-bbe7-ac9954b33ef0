import { useCallback, useEffect, useRef, useState } from 'react'

export function useElBounding(
  targetRef: React.RefObject<HTMLElement>,
  options: UseElBoundingOptions = {},
) {
  const {
    reset = true,
    windowResize = true,
    windowScroll = true,
    immediate = true,
    updateTiming = 'sync',
  } = options

  const [bounds, setBounds] = useState({
    height: 0,
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
    width: 0,
    x: 0,
    y: 0,
  })

  const observerRef = useRef<MutationObserver | null>(null)
  const resizeObserverRef = useRef<ResizeObserver | null>(null)

  const recalculate = useCallback(() => {
    const el = targetRef.current

    if (!el) {
      if (reset) {
        setBounds({
          height: 0,
          bottom: 0,
          left: 0,
          right: 0,
          top: 0,
          width: 0,
          x: 0,
          y: 0,
        })
      }
      return
    }

    const rect = el.getBoundingClientRect()

    setBounds({
      height: rect.height,
      bottom: rect.bottom,
      left: rect.left,
      right: rect.right,
      top: rect.top,
      width: rect.width,
      x: rect.x,
      y: rect.y,
    })
  }, [targetRef, reset])

  const update = useCallback(() => {
    if (updateTiming === 'sync') {
      recalculate()
    }
    else if (updateTiming === 'next-frame') {
      requestAnimationFrame(() => recalculate())
    }
  }, [recalculate, updateTiming])

  useEffect(() => {
    const el = targetRef.current

    if (el) {
      resizeObserverRef.current = new ResizeObserver(() => {
        update()
      })
      resizeObserverRef.current.observe(el)

      observerRef.current = new MutationObserver(update)
      observerRef.current.observe(el, {
        attributeFilter: ['style', 'class'],
      })

      /** 初始更新 */
      if (immediate) {
        update()
      }
    }

    return () => {
      resizeObserverRef.current?.disconnect()
      observerRef.current?.disconnect()
    }
  }, [targetRef, immediate, update])

  /** 监听窗口事件 */
  useEffect(() => {
    if (windowScroll) {
      window.addEventListener('scroll', update, { capture: true, passive: true })
    }
    if (windowResize) {
      window.addEventListener('resize', update, { passive: true })
    }

    return () => {
      if (windowScroll) {
        window.removeEventListener('scroll', update, { capture: true })
      }
      if (windowResize) {
        window.removeEventListener('resize', update)
      }
    }
  }, [windowScroll, windowResize, update])

  return {
    ...bounds,
    update,
  }
}

export type UseElBoundingReturn = ReturnType<typeof useElBounding>

export interface UseElBoundingOptions {
  reset?: boolean
  windowResize?: boolean
  windowScroll?: boolean

  /**
   * 组件挂载时立即调用更新
   *
   * @default true
   */
  immediate?: boolean
  updateTiming?: 'sync' | 'next-frame'
}
