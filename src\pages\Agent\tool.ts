import type { BaseChatParams, LechatParams } from '@/api/ChatApi'
import type { AddTextParams } from '@/utils'
import type { LogEntry } from '@/worker/parseAgent'
import type { Msg } from './types'
import type { SrcObj } from './useChatData'
import { ChatApi } from '@/api/ChatApi'
import { ChatGPTApi, Role } from '@/api/ChatGPTApi'
import { addText, drawBgImg, txtToJson } from '@/utils'
import { createCvs } from '@jl-org/cvs'
import { Canvas } from 'fabric'
import { nanoid } from 'nanoid'

import { editorMultiplier, showEditorHeight, showEditorWidth } from './constants'

export function genMsg(extra?: Partial<Msg>, srcs?: SrcObj[]): Msg {
  const files = srcs
    ? srcs.map(src => ({
        base64: src.base64,
        rawUrl: src.rawUrl || '',
        file: new File([], ''),
      }))
    : []

  return {
    content: '',
    id: nanoid(),
    type: 'answer',
    role: Role.Assistant,
    timestamp: Date.now(),
    files,
    ...extra,
  }
}

/**
 * 将日志条目转换为HTML格式的Markdown字符串
 * @param logEntries 日志条目数组
 * @returns 格式化后的Markdown字符串
 */
export function formatLogsToMarkdown(logEntries: readonly LogEntry[]): string {
  if (!logEntries || logEntries.length === 0) {
    return ''
  }

  return logEntries.map((entry) => {
    /** 根据日志级别设置不同的样式 */
    let levelStyle = ''
    let levelBadge = ''

    switch (entry.level) {
      case 'ERROR':
        levelStyle = 'color: #e53e3e; background-color: #fed7d7; padding: 2px 6px; border-radius: 4px;'
        levelBadge = '❌'
        break
      case 'WARNING':
        levelStyle = 'color: #d97706; background-color: #fef3c7; padding: 2px 6px; border-radius: 4px;'
        levelBadge = '⚠️'
        break
      case 'INFO':
        levelStyle = 'color: #3182ce; background-color: #e6f2ff; padding: 2px 6px; border-radius: 4px;'
        levelBadge = 'ℹ️'
        break
      case 'DEBUG':
        levelStyle = 'color: #4a5568; background-color: #edf2f7; padding: 2px 6px; border-radius: 4px;'
        levelBadge = '🔍'
        break
      default:
        levelStyle = 'color: #4a5568; background-color: #edf2f7; padding: 2px 6px; border-radius: 4px;'
        levelBadge = '📝'
    }

    /** 格式化时间戳 */
    const timestamp = `<span style="color: #718096; font-size: 0.75rem;">${entry.timestamp}</span>`

    /** 格式化日志级别 */
    const level = `<span style="${levelStyle}">${levelBadge} ${entry.level}</span>`

    /** 格式化来源 */
    const source = `<span style="color: #4a5568; font-weight: 600; font-size: 0.875rem;">${entry.source}</span>`

    /** 格式化消息内容 */
    let message = entry.message

    /** 检测并高亮代码块 */
    message = message.replace(/```([\s\S]*?)```/g, '<pre style="background-color: #f7fafc; padding: 8px; border-radius: 4px; overflow-x: auto;"><code>$1</code></pre>')

    /** 检测并高亮内联代码 */
    message = message.replace(/`([^`]+)`/g, '<code style="background-color: #f7fafc; padding: 2px 4px; border-radius: 2px;">$1</code>')

    /** 组合所有元素 */
    return `<div style="margin-bottom: 8px; font-family: monospace;">
      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
        ${timestamp}
        ${level}
        ${source}
      </div>
      <div style="white-space: pre-wrap; margin-left: 8px; border-left: 2px solid #e2e8f0; padding-left: 8px;">
        ${message}
      </div>
    </div>`
  }).join('\n')
}

export async function markdownToJSONPrompt<T>(params: BaseChatParams, fallback: T, jsonFormat: string) {
  const txt = `Please convert my markdown to JSON and do not answer unnecessary questions. I need to parse it directly using JSON.parse. If you cannot recognize it, use an empty string. The format is: ${jsonFormat}. ${params.content}`

  const json = await ChatApi.leChat({
    ...params,
    content: txt,
  })
  // const json = await ChatGPTApi.streamGpt4({
  //   ...params,
  //   content: txt,
  // })

  return txtToJson(json, fallback, false)
}

export function restorePoster(canvas: Canvas, msg: Msg) {
  const url = msg.files[0].rawUrl
  url && drawBgImg(canvas, url, {
    autoFit: true,
    center: true,
  })

  msg.posterLayout?.forEach((item) => {
    addText(canvas, item)
  })
}

export async function genPoster(
  param: TypographyParam,
) {
  const { cvs } = createCvs(showEditorWidth, showEditorHeight)
  const fabric = new Canvas(cvs, {
    width: showEditorWidth,
    height: showEditorHeight,
  })

  await drawBgImg(fabric, param.src, {
    autoFit: true,
    center: true,
  })

  param.posterLayout?.forEach((item) => {
    addText(fabric, item)
  })

  return fabric.toDataURL({
    multiplier: editorMultiplier,
  })
}

export function type(content: string, cb: (msg: string) => void, startIndex = 0, speed = 8) {
  let i = startIndex
  const { promise, resolve } = Promise.withResolvers<void>()
  const interval = setInterval(() => {
    cb(content.slice(0, i))
    if (i >= content.length) {
      clearInterval(interval)
      resolve()
    }
    i++
  }, speed)

  return promise
}

export type ProductDesc = {
  scene: string[]
  captions: {
    mainTitle: string
    subTitle: string
  }[]
}

export type TypographyParam = {
  src: string
  posterLayout: AddTextParams[]
}
