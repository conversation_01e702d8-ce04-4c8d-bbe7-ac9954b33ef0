import type { EChartsOption } from 'echarts/types/dist/shared'
import type { ChartItem, MarketResearch } from '../types'

import { echarts } from '@/utils/echarts'
import cx from 'clsx'
import { ageId, pieHeight, pieWidth, priceId, regionId } from '../constants'

const PieChart = memo<PieChartProps>(({
  data = [],
  title = '',
  radius = ['40%', '70%'],
  showLegend = false,
  className = '',
}) => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartRef.current)
      return

    const chart = echarts.init(chartRef.current)
    const convertedData = data.map(item => ({
      ...item,
      value: Number.parseFloat(item.value.replace('%', '')),
    }))

    const option: EChartsOption = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 14,
          color: '#333',
        },
      },
      tooltip: {
        trigger: 'item',
        // @ts-ignore
        formatter: ({ name, value, percent }) =>
          `${name}: ${value}% (${percent}%)`,
      },
      legend: showLegend
        ? {
            orient: 'vertical',
            right: 10,
            top: 'middle',
            textStyle: { color: '#666' },
          }
        : undefined,
      series: [
        {
          type: 'pie',
          radius,
          data: convertedData,
          label: {
            color: '#333',
            formatter: '{b}: {d}%',
          },
          labelLine: {
            lineStyle: {
              color: '#666',
            },
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
        },
      ],
    }

    chart.setOption(option)

    const handleResize = () => chart.resize()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chart.dispose()
    }
  }, [data, title, radius, showLegend])

  return <div ref={ chartRef } className={ cx('size-full', className) } />
})

export const PieCharts = memo<{ data: MarketResearch, className?: string }>(({ data, className }) => {
  return (
    <div
      style={ {
        width: pieWidth,
      } }
      className={ cx('size-full bg-lightBg', className) }
    >
      {/* 价格分布 */ }
      <div
        style={ {
          height: pieHeight,
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '16px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        } }
        id={ priceId }
        className="my-4"
      >
        <PieChart
          data={ data.priceDistribution }
          title="Price Distribution"
        />
      </div>

      {/* 年龄分布 */ }
      <div
        style={ {
          height: pieHeight,
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '16px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        } }
        id={ ageId }
        className="my-4"
      >
        <PieChart
          data={ data.ageDistribution }
          title="Age Distribution"
          showLegend={ false }
        />
      </div>

      {/* 地区分布 */ }
      <div
        style={ {
          height: pieHeight,
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '16px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        } }
        id={ regionId }
        className="my-4"
      >
        <PieChart
          data={ data.regionalDistribution }
          title="Regional Distribution"
          radius={ ['50%', '80%'] }
        />
      </div>
    </div>
  )
})

interface PieChartProps {
  data: ChartItem[]
  title?: string
  colors?: string[]
  radius?: [string | number, string | number]
  showLegend?: boolean
  className?: string
}
