import type { CSSProperties } from 'react'
import type { ColumnQueue, ItemRect, RenderItem, VirtualWaterFallProps, WaterfallItem } from './types'
import { onMounted } from '@/hooks'
import { rafThrottle } from '@jl-org/tool'
import { useUpdateEffect } from 'ahooks'
import classnames from 'clsx'
import { useMemo, useRef, useState } from 'react'

function InnerVirtualWaterfall<T extends WaterfallItem>({
  data,
  col = 5,
  gap = 10,

  prevBuffer = 400,
  nextBuffer = 400,

  hasMore,
  pageSize,
  loadMore,

  children,
  className,
  style,
}: VirtualWaterFallProps<T>) {
  const containerRef = useRef<null | HTMLDivElement>(null)

  const loading = useRef(false)
  const [scrollState, setScrollState] = useState({
    clientWidth: 0,
    clientHeight: 0,
    scrollTop: 0,
  })

  const bottom = useMemo(() => scrollState.clientHeight + scrollState.scrollTop, [scrollState])
  const [listStyle, setListStyle] = useState<CSSProperties>({})

  const [queueState, setQueueState] = useState({
    queue: new Array(col).fill(0).map<ColumnQueue>(() => ({ list: [], height: 0 })),
    len: 0,
  })

  const cardList = useMemo(
    () => queueState.queue.reduce<RenderItem<T>[]>((pre, { list }) => pre.concat(list), []),
    [queueState.queue],
  )
  const renderList = useMemo(
    () => cardList.filter(i =>
      i.h + i.y > scrollState.scrollTop - prevBuffer
      && i.y < bottom + nextBuffer,
    ),
    [cardList, bottom, prevBuffer, nextBuffer],
  )

  const itemSizeInfo = useMemo(() => {
    return data.reduce<Map<WaterfallItem['id'], ItemRect>>((pre, current) => {
      const itemWidth = Math.floor((scrollState.clientWidth - (col - 1) * gap) / col)
      pre.set(current.id, {
        width: itemWidth,
        height: Math.floor((itemWidth * current.height) / current.width),
      })

      return pre
    }, new Map())
  }, [data])

  const getComputedHeight = () => {
    let minIndex = 0
    let minHeight = Infinity
    let maxHeight = -Infinity

    queueState.queue.forEach(({ height, list }, index) => {
      const totalHeightWithGap = height + gap * (list.length - 1)

      if (totalHeightWithGap < minHeight) {
        minHeight = totalHeightWithGap
        minIndex = index
      }
      if (totalHeightWithGap > maxHeight) {
        maxHeight = totalHeightWithGap
      }
    })

    setListStyle({
      height: `${maxHeight}px`,
      contentVisibility: 'auto',
      containIntrinsicWidth: '100%',
      containIntrinsicHeight: `${scrollState.clientHeight}px`,
    })

    return {
      minIndex,
      minHeight,
    }
  }

  const genItem = (item: WaterfallItem, before: RenderItem | null, index: number): RenderItem => {
    const rect = itemSizeInfo.get(item.id)
    const width = rect!.width
    const height = rect!.height
    let y = 0
    if (before)
      y = before.y + before.h + gap

    return {
      item,
      y,
      h: height,
      style: {
        width: `${width}px`,
        height: `${height}px`,
        transform: `translate3d(${index === 0
          ? 0
          : (width + gap) * index
        }px, ${y}px, 0)`,
      },
    }
  }

  const addInQueue = (size = pageSize) => {
    const queue = queueState.queue
    let len = queueState.len

    for (let i = 0; i < size; i++) {
      if (len >= data.length)
        break

      const minIndex = getComputedHeight().minIndex
      const currentColumn = queue[minIndex]

      const before = currentColumn.list[currentColumn.list.length - 1] || null
      const lastItem = data[len]
      const item = genItem(lastItem, before, minIndex)

      currentColumn.list.push(item)
      currentColumn.height += item.h
      len++
    }
    setQueueState({ queue: [...queue], len })
    getComputedHeight()
  }

  const loadDataList = async () => {
    if (!hasMore || loading.current)
      return
    loading.current = true

    loadMore().finally(() => {
      loading.current = false
    })
  }

  const handleScroll = rafThrottle(() => {
    const { scrollTop, clientHeight } = containerRef.current!
    setScrollState({ ...scrollState, scrollTop })

    if (scrollTop + clientHeight > getComputedHeight().minHeight) {
      loadDataList()
    }
  })

  const initScrollState = () => {
    const { scrollTop, clientHeight, clientWidth } = containerRef.current!
    setScrollState({ scrollTop, clientHeight, clientWidth })
  }

  useUpdateEffect(() => {
    if (
      listStyle.height
      && scrollState.clientHeight >= Number.parseFloat(`${listStyle.height}`)
    ) {
      loadDataList()
    }
  }, [listStyle.height, scrollState.clientHeight])

  useUpdateEffect(() => {
    if (data.length === 0) {
      /** 重置所有状态 */
      setQueueState({
        queue: new Array(col).fill(0).map<ColumnQueue>(() => ({ list: [], height: 0 })),
        len: 0,
      })
      setListStyle({})
      loadDataList()
    }

    itemSizeInfo.size && addInQueue()
  }, [data, itemSizeInfo, pageSize])

  onMounted(() => {
    initScrollState()
    loadDataList()
  })

  return <div
    className={ classnames(
      'w-full h-full overflow-y-scroll overflow-x-hidden',
      className,
    ) }
    style={ style }
    data-id="scrollList"
    ref={ containerRef }
    onScroll={ handleScroll }>

    <div className="relative w-full" style={ listStyle }>
      { renderList.map(({ item, style }, index) => (
        <div
          className="absolute left-0 top-0 box-border"
          key={ item.id }
          style={ style }
        >

          { children(item, index) }
        </div>
      )) }
    </div>
  </div>
}

InnerVirtualWaterfall.displayName = 'VirtualWaterfall'
export const VirtualWaterfall = memo(InnerVirtualWaterfall) as typeof InnerVirtualWaterfall
