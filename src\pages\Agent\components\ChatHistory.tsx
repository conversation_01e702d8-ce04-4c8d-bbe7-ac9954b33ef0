import type { ChatHistoryProps } from '../types'
import clsx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import React, { useEffect, useRef } from 'react'
import { ChatMessage } from './ChatMessage'

export const ChatHistory = memo<ChatHistoryProps>(({
  messages,
  onLoadMore,
  hasMore,
  isLoading,
  className,
  style,
  needScroll,
  setNeedScroll,
  ...rest
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (loadingRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore) {
            onLoadMore()
          }
        },
        { threshold: 0.5 },
      )

      observerRef.current.observe(loadingRef.current)
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [hasMore, onLoadMore])

  useEffect(() => {
    if (!containerRef.current || !needScroll)
      return

    setNeedScroll?.(true)
    containerRef.current.scrollTop = containerRef.current.scrollHeight
  }, [messages, needScroll, setNeedScroll])

  return (
    <div
      ref={ containerRef }
      className={ clsx('chat-history flex flex-col gap-12 overflow-y-auto overflow-x-hidden p-6', className) }
      style={ {
        ...style
      } }
      { ...rest }
    >
      { hasMore && (
        <motion.div
          ref={ loadingRef }
          initial={ { opacity: 0 } }
          animate={ { opacity: 1 } }
          className="flex items-center justify-center"
        >
          <motion.div
            animate={ { rotate: 360 } }
            transition={ { duration: 1, repeat: Infinity, ease: 'linear' } }
          >
            <Loader2 className="h-6 w-6 text-violet-500" />
          </motion.div>
        </motion.div>
      ) }
      <AnimatePresence initial={ false }>
        { messages.map(message => (
          <ChatMessage
            key={ message.id }
            message={ message }
            isLoading={ message.type === 'loading' && isLoading }
          />
        )) }
      </AnimatePresence>
    </div>
  )
})
