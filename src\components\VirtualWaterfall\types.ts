import type { CSSProperties } from 'react'

export type VirtualWaterFallProps<T extends WaterfallItem> = {
  data: T[]

  gap?: number

  col?: number

  pageSize: number
  hasMore: boolean

  prevBuffer?: number

  nextBuffer?: number

  loadMore: () => Promise<any>

  children: (detail: WaterfallItem & T, index: number) => React.ReactElement
  className?: string
  style?: CSSProperties
}

export type WaterfallItem = {
  id: number | string
  width: number
  height: number
  [key: string]: any
}

export type ColumnQueue = {
  list: RenderItem[]
  height: number
}

export type RenderItem<T = any> = {

  item: WaterfallItem & T

  y: number

  h: number

  style: CSSProperties
}

export type ItemRect = {
  width: number
  height: number
}
