import type { FileItem } from '@/components/Uploader'
import type { Msg } from './types'
import { ChatApi } from '@/api/ChatApi'
import { Role } from '@/api/ChatGPTApi'
import { genArr } from '@jl-org/tool'
import { nanoid } from 'nanoid'
import { chatStore } from './store'
import { genMsg, type } from './tool'

export function useChatData() {
  const snap = chatStore.useAndDispose()
  const isAWS = snap.mode === 'awsShop'

  const [messages, setMessages] = useState<Msg[]>([])
  const [isLoading, setIsLoading] = useState(false)

  async function thinkDone(id: string, extra?: Partial<Msg>) {
    let text = ''
    return new Promise<string>((resolve) => {
      setMessages((prev) => {
        return prev.map((item) => {
          if (item.id === id) {
            text = item.content
            resolve(text)

            return {
              ...item,
              type: 'thinkDone',
              completed: true,
              ...extra,
            }
          }
          return item
        })

        /**
         * 进度放在最底下
         */
        // return [
        //   ...prev.filter(msg => !['thinking', 'progress'].includes(msg.type)),
        //   {
        //     ...thinkMsg,
        //     type: 'thinkDone'
        //   },
        //   ...prev.filter(msg => ['progress'].includes(msg.type)),
        // ]
      })
    })
  }

  function genThink(content: string, extra?: Partial<Msg>) {
    const data: Msg = {
      ...genMsg(),
      content,
      type: 'thinking',
      role: Role.Assistant,
      ...extra,
    }

    return data
  }

  function sendTink(extra?: Partial<Msg>) {
    const data = genThink('', extra)
    setMessages(prev => [...prev, data])
    return data.id
  }

  function updateThink(content: string, id: string) {
    setMessages((prev) => {
      return prev.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            content,
          }
        }
        return item
      })

      /**
       * 进度放在最底下
       */
      // return [
      //   ...prev.filter(msg => !['thinking', 'progress'].includes(msg.type)),
      //   {
      //     ...thinkMsg,
      //     content
      //   },
      //   ...prev.filter(msg => ['progress'].includes(msg.type)),
      // ]
    })
  }

  async function thinkData(content: string, extra?: Partial<Msg>, update = updateThink) {
    const id = sendTink(extra)
    await type(content, () => update(content, id))
    return thinkDone(id)
  }

  function genProgress(num = 1, msg?: Partial<Msg>) {
    const id = nanoid()
    const genData = () => {
      const data: Msg = {
        ...genMsg(),
        id,
        type: 'progress',
        role: Role.Assistant,
        ...msg,
      }
      return data
    }

    setMessages(prev => [...prev, ...genArr(num, () => genData())])
    return id
  }

  function loadingAnswer() {
    const id = nanoid()
    const data: Msg = {
      ...genMsg(),
      id,
      role: Role.Assistant,
      type: 'loading',
    }
    setMessages(prev => [...prev, data])

    return id
  }

  function updateAnswer(id: string, content: string, extra?: Partial<Msg>) {
    setMessages((prev) => {
      return prev.map((item) => {
        return item.id === id
          ? {
              ...item,
              content,
              timestamp: Date.now() + 1,
              type: 'answer',
              ...extra,
            }
          : item
      })
    })
  }

  function filterHistory() {
    if (isAWS) {
      return []
    }

    return messages
      .filter(item => item.type !== 'loading' && item.content && item.mode !== 'awsShop')
      .map(item => ({
        content: item.content,
        role: item.role,
      }))
  }

  function onFail(msg?: string) {
    setMessages((prev) => {
      return [
        ...prev,
        {
          id: nanoid(),
          content: msg ?? 'Response failed, please try again later.',
          type: 'answer',
          timestamp: Date.now() + 1,
          role: Role.Assistant,
          files: [],
        },
      ]
    })
  }

  function sendMsg(msg?: Partial<Msg>, srcs?: SrcObj[]) {
    const files = srcs
      ? srcs.map(src => ({
          base64: src.base64,
          rawUrl: src.rawUrl || '',
          file: new File([], ''),
        }))
      : []

    setMessages([
      {
        content: '',
        id: nanoid(),
        type: 'answer',
        role: Role.User,
        timestamp: Date.now(),
        files,
        ...msg,
      },
    ])
  }

  function updateMsgById(id: string, msg: Partial<Msg>, srcs?: SrcObj[]) {
    const files = srcs
      ? srcs.map(src => ({
          base64: src.base64,
          rawUrl: src.rawUrl || '',
          file: new File([], ''),
        }))
      : []

    setMessages(pre => pre.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          ...msg,
          files,
        }
      }
      return item
    }))
  }

  /**
   * 发送消息聊天
   */
  const handleSendMessage = async (content: string, files: FileItem[]) => {
    setIsLoading(true)

    const userMessage: Msg = {
      ...genMsg(),
      content,
      role: Role.User,
      type: 'answer',
      files: files.map(item => ({
        ...item,
        rawUrl: '',
      })),
    }
    setMessages(prev => [...prev, userMessage])

    // Add temporary loading message
    const id = loadingAnswer()

    try {
      const imgUrl = files.length
        ? files[0].base64
        : undefined
      await ChatApi.leChat({
        content,
        onMsg: txt => updateAnswer(id, txt),
        imgUrl,
        history: filterHistory(),
      })
    }
    catch (error: any) {
      onFail()
    }
    finally {
      setIsLoading(false)
    }
  }

  return {
    snap,
    isAWS,
    messages,
    isLoading,

    setIsLoading,
    setMessages,

    sendMsg,
    sendTink,
    updateMsgById,

    updateThink,
    genThink,
    thinkDone,
    thinkData,

    loadingAnswer,
    updateAnswer,
    genProgress,

    onFail,

    filterHistory,
    handleSendMessage,
  }
}

export type SrcObj = {
  base64: string
  rawUrl?: string
}
