.login-modal {
  :global {
    .ant-modal-body {
      padding: 0 !important;
    }
  }

  .login-container {
    display: flex;
    justify-content: center;
    background: url(@/assets/image/login/loginbg.webp) no-repeat center / 110% 120%;

    .login-content {
      width: 342px;

      .login-logo {
        margin: 40px auto;
      }

      .email-login {
        // margin-bottom: var(--ant-margin-lg);
        margin-bottom: 10px;
        margin-top: 20px;
        font-weight: 600;
        font-size: var(--ant-font-size-lg);
      }

      .login-form-forgot {
        text-align: right;

        .text {
          // color: var(--ant-color-text-tertiary);
          line-height: 36px;
          color: #292F37;

          &:hover {
            color: var(--ant-color-primary);
          }
        }
      }

      .sign-up-text {
        margin-left: var(--ant-margin-sm);
        cursor: pointer;
      }

      .agreement {
        margin-top: 46px;
        text-align: center;
      }
    }
  }


  .forget-verify {
    margin: 0 auto;
    text-align: center;

    .forget-pwd {
      font-size: 16px;
      font-weight: 600;
    }

    .verify-code {
      font-size: 12px;
      color: var(--ant-color-text-secondary);
      margin-top: 5px;
    }
  }

  .forget-btn-sure {
    margin: 60px auto;
  }

  .have-count-content {
    margin-bottom: 50px;
  }

  .to-sign-up {
    color: #126DD7;
    font-size: 14px;
    cursor: pointer;
  }

  .have-count {
    color: var(--ant-color-text-secondary);
    font-size: 14px;
  }
}