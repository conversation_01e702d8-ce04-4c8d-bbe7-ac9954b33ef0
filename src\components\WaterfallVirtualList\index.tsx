import type {
  CSSProperties,
  ReactNode,
} from 'react'
import { DEFAULT_MATERIAL_SIZE } from '@/config'
import { Flex } from 'antd'
import { isNumber, minBy } from 'lodash-es'

import {
  startTransition,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { columnGap, computeColumnWidth, computeMaxColumns } from './util'

export interface Props<T extends WaterfallCellVO> {
  /** 列表源数据 */
  list: T[]
  /** item render function */
  renderItem: (item: WaterfallColumnCell<T>, index: number) => ReactNode
  /** 列数 */
  columnCount?: number
  /** 行间隙 */
  rowGap?: number
  /** scrollTop */
  scrollTop?: number
  /** offsetHeight */
  offsetHeight?: number
  /** relative position */
  useOffsetTop?: boolean
}

function WaterfallVirtualList<T extends WaterfallCellVO>({
  list,
  renderItem,
  columnCount = 5,
  rowGap = 0,
  scrollTop = 0,
  offsetHeight = 0,
  useOffsetTop = false,
}: Props<T>) {
  const waterfallWrapperRef = useRef<HTMLDivElement>(null)

  const [waterfallWrapperWidth, setWaterfallWrapperWidth] = useState(0)

  const listColumns = useMemo(() => {
    return computeMaxColumns(waterfallWrapperWidth, columnCount)
  }, [waterfallWrapperWidth, columnCount])

  const waterfallList: WaterfallColumn<T>[] = useMemo(() => {
    /** 初始化瀑布流的内容列表和高度列表 */
    const columns: WaterfallColumn<T>[] = Array.from(
      { length: listColumns },
      (_, i) => ({
        columnIndex: i,
        prevPlaceholderHeight: 0,
        nextPlaceholderHeight: 0,
        height: 0,
        list: [] as WaterfallColumnCell<T>[],
      }),
    )

    const columnWidth = computeColumnWidth(waterfallWrapperWidth, listColumns)

    /** 列表容器的初始top */
    const listWrapperOffsetTop = useOffsetTop
      ? waterfallWrapperRef.current?.offsetTop || 0
      : 0

    /** 滚动容器可视区域上下线top */
    const [viewportStartTop, viewportEndTop] = [
      scrollTop,
      scrollTop + offsetHeight,
    ]

    for (let i = 0; i < list.length; i++) {
      const item = list[i]

      /** 高度没有的情况兼容 */
      const [itemWidth, itemHeight] = [
        item.width || DEFAULT_MATERIAL_SIZE[0],
        item.height || DEFAULT_MATERIAL_SIZE[1],
      ]

      /** 等比计算元素的高度 */
      const computedHeight = (columnWidth * itemHeight) / itemWidth

      /** 找到高度最小的列 */
      const minHeightColumnIndex = minBy(columns, 'height')?.columnIndex || 0

      /** 元素将要添加到的列 */
      const targetColumn = columns[minHeightColumnIndex]

      /** 元素在其所在列的index */
      const rowIndex = targetColumn.list.length

      /** 根据容器滚动信息计算元素的上下线top值用于判断其是否在可视区域 */
      const startTop = listWrapperOffsetTop + targetColumn.height
      const endTop = startTop + computedHeight

      // TODO: 可考虑增加buffer偏移量判断优化
      /** 判断元素是否离开可视区域上线 */
      if (viewportStartTop > endTop) {
        /** 从上线刚好离开可视区域的元素index+1为在可视区域的起始slice index */
        targetColumn.startIndex = rowIndex + 1
        /** 更新占位高度 */
        targetColumn.prevPlaceholderHeight
          = targetColumn.height + computedHeight + rowGap
      }
      /** 判断元素是否离开可是区域下线 */
      if (viewportEndTop < startTop) {
        /** 从下线刚好离开可视区域的元素index为在可视区域的末尾slice index */
        if (!targetColumn.endIndex) {
          targetColumn.endIndex = rowIndex
        }
        /** 记录占位高度 */
        targetColumn.nextPlaceholderHeight += computedHeight + rowGap
      }

      /** 累计列高度 */
      targetColumn.height += computedHeight + rowGap

      /** 添加到对应列 */
      targetColumn.list.push({ ...item, _computedHeight: computedHeight })
    }

    return columns
  }, [
    listColumns,
    waterfallWrapperWidth,
    list,
    scrollTop,
    offsetHeight,
    rowGap,
    useOffsetTop,
  ])

  useEffect(() => {
    const windowObserver = new ResizeObserver(() => {
      startTransition(() => {
        const listWrapper = waterfallWrapperRef.current
        if (listWrapper) {
          setWaterfallWrapperWidth(listWrapper.clientWidth)
        }
      })
    })
    const listWrapper = waterfallWrapperRef.current
    if (listWrapper) {
      windowObserver.observe(listWrapper)
    }
  }, [])

  const columnStyle: CSSProperties = {
    /** 根据列数计算列宽度比例 */
    width: `calc(${100 / listColumns}% - ${columnGap - columnGap / listColumns}px)`,
  }

  return (
    <Flex gap={ columnGap } ref={ waterfallWrapperRef }>
      {waterfallList.map(
        ({
          columnIndex,
          list,
          prevPlaceholderHeight,
          nextPlaceholderHeight,
          startIndex,
          endIndex,
        }) => (
          <Flex vertical key={ columnIndex } style={ columnStyle }>
            {/* prev 高度占位 */}
            <div style={ { height: prevPlaceholderHeight } } />

            {/* 截取虚拟列表出现在视窗的内容 */}
            {(isNumber(startIndex) || isNumber(endIndex)
              ? list.slice(startIndex || 0, endIndex)
              : list
            ).map(renderItem)}

            {/* next 高度占位 */}
            <div style={ { height: nextPlaceholderHeight } } />
          </Flex>
        ),
      )}
    </Flex>
  )
}

export default memo(WaterfallVirtualList) as typeof WaterfallVirtualList

export interface WaterfallCellVO {
  /** 宽度 */
  width: number | null
  /** 高度 */
  height: number | null
}

type WaterfallColumn<T> = {
  /** 该列index */
  columnIndex: number
  /** 该列总高度 */
  height: number
  /** 该列元素出现在视窗的起始元素index */
  startIndex?: number
  /** 该列元素出现在视窗的末尾元素index */
  endIndex?: number
  /** 该列起始占位元素高度 */
  prevPlaceholderHeight: number
  /** 该列末尾占位元素高度 */
  nextPlaceholderHeight: number
  /** 该列包含的列数据 */
  list: WaterfallColumnCell<T>[]
}

export type WaterfallColumnCell<T> = T & {
  /** 元素的实际计算显示高度 */
  _computedHeight: number
}
