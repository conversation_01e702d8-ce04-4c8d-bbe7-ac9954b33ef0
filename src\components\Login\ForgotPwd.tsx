import type { CSSProperties } from 'react'
import { Button, Form, Input, Typography } from 'antd'
import classnames from 'clsx'
import { Eye, EyeOff } from 'lucide-react'
import { memo } from 'react'
import { Agree } from './Agree'
import { useLogin } from './useLogin'

const { Text, Link } = Typography

function _ForgotPwd({
  style,
  className,
  onSignIn,
}: ForgotPwdProps) {
  const [form] = Form.useForm()
  const { onForgotPwd, sendCode, disabled, loading, time } = useLogin(form)

  return <Form
    className={ classnames(
      'flex flex-col',
      className,
    ) }
    style={ style }
    form={ form }
    onFinish={ (v) => {
      onForgotPwd(v).then(() => {
        onSignIn?.()
      })
    } }
  >
    <div className="h-12 text-center">
      <Text className="font-semibold">Forget password</Text>
    </div>

    <div>
      <Text className="mb-4 block !text-blue-500">Email Address</Text>
      <Form.Item name="email" rules={ [{ required: true, message: 'Please input your email!' }] }>
        <Input
          allowClear
          placeholder="Email"
          className="!h-10 !rounded-xl"
        />
      </Form.Item>
    </div>

    <div className="w-full flex gap-4">
      <Form.Item name="captcha" rules={ [{ required: true, message: 'Please input your verification code!' }] }>
        <Input
          allowClear
          placeholder="Verification Code"
          className="!h-10 !flex-1 !rounded-xl"
        />
      </Form.Item>
      <div
        className={ `flex items-center justify-center w-32 h-10 bg-[#F2F3F6] text-nowrap px-4 rounded-xl !text-blue-500 hover:opacity-70 transition duration-300 text-center cursor-pointer
        ${disabled && '!opacity-70 !cursor-not-allowed'}` }
        onClick={ sendCode }
      >
        {
          disabled
            ? time
            : 'Send Code'
        }
      </div>
    </div>

    <Form.Item
      name="password"
      rules={ [{ required: true, message: 'Please input your password!' }] }
    >
      <Input.Password
        allowClear
        placeholder="Password"
        className="!h-10 !rounded-xl"
        iconRender={ visible => (visible
          ? <Eye />
          : <EyeOff />) }
      />
    </Form.Item>

    <Form.Item
      name="confirm"
      dependencies={ ['password'] }
      rules={ [
        { required: true, message: 'Please confirm your password!' },
        ({ getFieldValue }) => ({
          validator(_, value) {
            if (!value || getFieldValue('password') === value) {
              return Promise.resolve()
            }
            return Promise.reject(new Error('The two passwords do not match!'))
          },
        }),
      ] }
    >
      <Input.Password
        allowClear
        placeholder="Confirm"
        className="!h-10 !rounded-xl"
        iconRender={ visible => (visible
          ? <Eye />
          : <EyeOff />) } />
    </Form.Item>

    <div className="text-light">
      Already have an account? &nbsp;
      <Link className="text-blue-500" onClick={ onSignIn }>Login</Link>
    </div>

    <div className="mt-auto">
      <Button
        type="primary"
        className="mt-2 w-full !h-10 !rounded-xl"
        loading={ loading }
        htmlType="submit"
      >
        Reset Password
      </Button>

      <Agree />
    </div>

  </Form>
}

export const ForgotPwd = memo<ForgotPwdProps>(_ForgotPwd)
ForgotPwd.displayName = 'ForgotPwd'

export type ForgotPwdProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode
  onSignIn?: () => void
}
