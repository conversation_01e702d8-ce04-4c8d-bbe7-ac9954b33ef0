.thumbnail-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f0f0f1;
}

.preview-image {
  object-fit: contain;
  // object-fit: cover;
  // max-width:325px;
}

.error-box {
  width: 100%;
  height: 100%;
  background: #f3f5f6;

  img {
    width: 138px;
    max-width: 60%;
  }

  .text {
    width: 138px;
    max-width: 100%;
    margin-top: var(--ant-margin-xs);
    color: var(--ant-color-text-secondary);
    font-size: var(--ant-font-size-sm);
    text-align: center;
  }
}

.left-side-img {
  position: relative;
  width: 100%;

  .overText {
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
    font-size: 12px;
    display: none;
    height: 20px;
    background: #000;
    opacity: 0.7;
    color: #fff;
  }
}

.left-side-img:hover {
  .overText {
    display: block;
  }
}