import clsx from 'clsx'
import { motion } from 'framer-motion'
import { memo, useEffect, useRef, useState } from 'react'

/**
 * 验证码输入组件
 */
export const VerificationCodeInput = memo(({
  value,
  onChange,
  onSendCode,
  placeholder = '请输入验证码',
  className,
  cooldownTime = 60,
  buttonText = '发送验证码',
  cooldownText = '{time}秒后重新发送',
}: VerificationCodeInputProps) => {
  const [isCooldown, setIsCooldown] = useState(false)
  const [cooldownRemaining, setCooldownRemaining] = useState(cooldownTime)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  /** 处理发送验证码 */
  const handleSendCode = async () => {
    if (isCooldown)
      return

    let shouldStartCooldown = true
    /** 调用发送验证码回调，并检查返回值 */
    const success = await onSendCode()
    // 如果 onSendCode 返回 false 或未定义，则不开始冷却
    if (success === false) {
      shouldStartCooldown = false
    }

    // 仅在验证成功后开始倒计时
    if (shouldStartCooldown) {
      /** 开始倒计时 */
      setIsCooldown(true)
      setCooldownRemaining(cooldownTime)

      timerRef.current = setInterval(() => {
        setCooldownRemaining((prev) => {
          if (prev <= 1) {
            /** 倒计时结束 */
            clearInterval(timerRef.current as NodeJS.Timeout)
            setIsCooldown(false)
            return cooldownTime
          }
          return prev - 1
        })
      }, 1000)
    }
  }

  /** 组件卸载时清除定时器 */
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [])

  /** 生成按钮文本 */
  const getButtonText = () => {
    if (isCooldown) {
      return cooldownText.replace('{time}', cooldownRemaining.toString())
    }
    return buttonText
  }

  return (
    <div className={ clsx('flex rounded-lg bg-[#F4F9FE] p-4 shadow-sm', className) }>
      <input
        type="text"
        className="flex-1 bg-transparent text-sm outline-none placeholder:text-gray-400"
        placeholder={ placeholder }
        value={ value }
        onChange={ e => onChange(e.target.value) }
      />
      <motion.button
        className={ clsx(
          'text-sm font-medium ml-4',
          isCooldown
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-blue-600 hover:text-blue-700',
        ) }
        onClick={ handleSendCode }
        whileHover={ isCooldown
          ? {}
          : { scale: 1.05 } }
        whileTap={ isCooldown
          ? {}
          : { scale: 0.95 } }
        disabled={ isCooldown }
      >
        { getButtonText() }
      </motion.button>
    </div>
  )
})

VerificationCodeInput.displayName = 'VerificationCodeInput'

export interface VerificationCodeInputProps {
  /**
   * 验证码值
   */
  value: string
  /**
   * 验证码变更回调
   */
  onChange: (value: string) => void
  /**
   * 发送验证码回调
   * @returns 返回布尔值表示是否发送成功（用于决定是否开始冷却）
   */
  onSendCode: () => Promise<boolean | void>
  /**
   * 占位符文本
   * @default '请输入验证码'
   */
  placeholder?: string
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 冷却时间（秒）
   * @default 60
   */
  cooldownTime?: number
  /**
   * 按钮文本
   * @default '发送验证码'
   */
  buttonText?: string
  /**
   * 冷却中按钮文本，使用{time}作为倒计时占位符
   * @default '{time}秒后重新发送'
   */
  cooldownText?: string
}
