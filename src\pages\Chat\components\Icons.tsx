import type { ShowMode } from '../store'
import { ClipboardList, Image, Rotate3d, TrendingUp, Video } from 'lucide-react'

export const iconStyle = { size: 16, strokeWidth: 1.2 }

export const iconMap: Record<ShowMode, React.ReactNode> = {
  '3dModel': <Rotate3d { ...iconStyle } />,
  'copyWrite': <ClipboardList { ...iconStyle } />,
  'img': <Image { ...iconStyle } />,
  'report': <TrendingUp { ...iconStyle } />,
  'video': <Video { ...iconStyle } />,
}
