import type { UploadProps } from 'antd'
import type { RcFile } from 'antd/lib/upload'
import { god } from '@/god'
import { getImageSize } from '@/utils'
import { useCallback } from 'react'
import { useT } from './useT'

const limit = 10000

export function useUploadImageValid() {
  const t = useT()

  const validUploadImageSize: Required<UploadProps>['beforeUpload']
    = useCallback(
      async (_, files) => {
        for (let i = 0; i < files.length; i++) {
          const size = await getImageSize(files[i])
          if (!size) {
            return true
          }
          const [width, height] = size
          if (limit < width || limit < height) {
            god.messageError(
              t('layout.image-size-limit-tips', {
                limitSize: `${limit}X${limit}`,
              }),
            )
            return false
          }
        }
        return true
      },
      [t],
    )

  const validImageSize = useCallback(
    async (files: File[]) => {
      if (files.length < 1) {
        return true
      }

      return validUploadImageSize(files[0] as RcFile, files as RcFile[])
    },
    [validUploadImageSize],
  )

  return { validImageSize, validUploadImageSize }
}
