import { god } from '@/god'
import cx from 'clsx'
import { ClipboardPen, Download } from 'lucide-react'
import { memo } from 'react'

export const Btns = memo<BtnsProps>((
  {
    style,
    className,
    onDonwload,
    onEdit,
    btnClassName,
    iconClass,
    disabled,
  },
) => {
  return <div
    className={ cx(
      'BtnsContainer absolute right-4 top-4 z-10 flex gap-2',
      className,
    ) }
    style={ style }
  >
    { onEdit && <button
      onClick={ () => {
        if (disabled) {
          god.messageWarn('Please wait')
          return
        }
        onEdit()
      } }
      className={ `transition-opacity hover:opacity-50 transition-all duration-300 bg-black/50 p-2 rounded-full ${btnClassName}` }
    >
      <ClipboardPen className={ `size-4 ${iconClass}` } color="white" />
    </button>}

    { onDonwload && <button
      onClick={ () => {
        if (disabled) {
          god.messageWarn('Please wait')
          return
        }
        onDonwload()
      } }
      className={ `transition-opacity hover:opacity-50 transition-all duration-300 bg-black/50 p-2 rounded-full ${btnClassName}` }
    >
      <Download className={ `size-4 ${iconClass}` } color="white" />
    </button>}
  </div>
})

Btns.displayName = 'Btns'

export type BtnsProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  onDonwload?: () => void
  onEdit?: () => void
  btnClassName?: string
  iconClass?: string
  disabled?: boolean
}
