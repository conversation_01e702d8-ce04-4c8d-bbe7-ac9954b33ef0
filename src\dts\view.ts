export type UnPromisify<T> = T extends Promise<infer U> ? U : never

export type ViewState = {
  /** 左侧边栏是否折叠 */
  leftSiderCollapsed: boolean
  /** 列表slider控制器value */
  listSliderValue: number
  /** 当前antd主题模式 */
  themeMode: ThemeMode
  /** locale */
  localeType: "zh-CN" | "en-US"
}

type ThemeMode = "dark" | "light"

export type AppLocalStorageShape = {
  /** 当前登录用户token */
  ["user_token"]: string
  /** 当前主题模式 */
  current_theme_mode: ThemeMode
}

export type PagerList<T = unknown> = {
  /** 分页数据 */
  list: T[]
  /** total count */
  total: number
}

export type PageQuery<T = unknown> = {
  page: number
  size: number
  sort?: 'createTime,desc' | 'createTime,asc' | {}
} & T
