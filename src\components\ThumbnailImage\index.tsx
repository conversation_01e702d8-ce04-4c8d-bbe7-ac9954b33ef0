import type {
  CSSProperties,
  FC,
  ImgHTMLAttributes,
} from 'react'
import errorImage from '@/assets/image/image-error.webp'
import { useT } from '@/hooks'
import { Flex, Image, Typography } from 'antd'
import classNames from 'clsx'

import {
  useCallback,
  useState,
} from 'react'
import styles from './index.module.less'

const ThumbnailImage: FC<Props> = ({
  src,
  name,
  className,
  style,
  withPreview = false,
  enableHover = true,
  showText = true,
  ...props
}) => {
  const t = useT()

  const [loadError, setLoadError] = useState(false)

  const onImageLoadError = useCallback(() => {
    setLoadError(true)
  }, [])

  if (src && !loadError) {
    return !withPreview
      ? (
          <div className={ styles['left-side-img'] }>
            <img
              src={ src }
              alt="image"
              className={ classNames(styles['thumbnail-image'], className) }
              style={ style }
              onError={ onImageLoadError }
              draggable="false"
              { ...props }
            />
            {enableHover && <div className={ styles.overText }>
              {' '}
              {name}
              {' '}
            </div>}
          </div>
        )
      : (
          <div style={ {
            position: 'relative',
          } }>
            <Image
              src={ src }
              alt="image"
              className={ classNames(styles['preview-image'], className) }
              width="100%"
              height="100%"
              style={ style }
              onError={ onImageLoadError }
              draggable="false"
            />
          </div>
        )
  }
  else {
    return (
      <Flex
        vertical
        className={ styles['error-box'] }
        justify="center"
        align="center"
      >
        <img src={ errorImage } alt="" />

        {showText && <Typography.Text className={ styles.text }>
          {t('layout.image-lost-tips')}
        </Typography.Text>}

      </Flex>
    )
  }
}

export default memo(ThumbnailImage)

type Props = ImgHTMLAttributes<HTMLImageElement> & {
  name?: string
  className?: string
  style?: CSSProperties
  withPreview?: boolean
  enableHover?: boolean
  showText?: boolean
}
