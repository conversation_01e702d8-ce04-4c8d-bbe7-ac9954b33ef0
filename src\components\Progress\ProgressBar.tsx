import { cn } from '@/utils'
import { clamp } from '@jl-org/tool'
import { memo } from 'react'

export const ProgressBar = memo<ProgressBarProps>((
  {
    style,
    className,
    value,
    colors = ['#3276F91A', '#01D0BD'],
    height = 5,
    animationDuration = 0.8,
    animationEase = 'easeOut',
  },
) => {
  const formatVal = clamp(value, 0, 1)

  /** 根据 colors 数组生成渐变背景 */
  const gradientBackground = `linear-gradient(to right, ${colors.join(', ')})`

  return <div className={ cn('ProgressBarContainer w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700', className) }>
    <div
      className={ cn('rounded-full w-full') }
      style={ {
        background: gradientBackground,
        height,
        transition: `${animationDuration}s ${animationEase}`,
        transform: `scaleX(${formatVal})`,
        transformOrigin: 'left center',
        ...style,
      } }
    />
  </div>
})

ProgressBar.displayName = 'ProgressBar'

export type ProgressBarProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  value: number
  colors?: string[]
  height?: number
  animated?: boolean
  animationDuration?: number
  animationEase?: string
}
