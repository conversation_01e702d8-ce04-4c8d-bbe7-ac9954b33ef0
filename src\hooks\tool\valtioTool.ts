import type { Snapshot } from 'valtio'
import { deepClone } from '@jl-org/tool'
import { proxy, useSnapshot } from 'valtio'
import { onUnmounted } from './lifecycle'

export function createProxy<T extends object>(initState: ExcludeProps & T) {
  const getInitState = () => deepClone(initState) as T
  const store = proxy(getInitState())

  const reset = (key?: keyof T, initState?: Partial<T>) => {
    const rawData = initState || getInitState()
    const whiteList: InternalMethods[] = ['use', 'useAndDispose', 'reset', 'dispose', 'getInitState']

    // @ts-ignore
    if (key && key in store && !whiteList.includes(key)) {
      // @ts-ignore
      store[key] = rawData[key]
      return
    }

    Object.keys(store).forEach((k) => {
      // @ts-ignore
      if (whiteList.includes(k) || !(k in rawData))
        return

      // @ts-ignore
      store[k] = rawData[k]
    })
  }

  const use = (options?: Options) => useSnapshot(store, options)
  const dispose = (initState?: Partial<T>) => onUnmounted(() => reset(undefined, initState))
  const useAndDispose = (options?: Options) => (dispose(), use(options))

  const tools: Tool<T> = {
    use,
    useAndDispose,

    reset,
    dispose,
    getInitState,
  }

  return Object.assign(store, tools)
}

export function createProxyValue<T>(value: T) {
  return createProxy({
    value,
  })
}

export type InternalMethods = keyof Tool<any>
type Options = Parameters<typeof useSnapshot>[1]

export interface Tool<T = any> {
  reset: (key?: keyof T, initState?: Partial<T>) => void
  use: (options?: Options) => Snapshot<T>
  useAndDispose: (options?: Options) => Snapshot<T>
  getInitState: () => T
  dispose: (initState?: Partial<T>) => void
}

type ExcludeProps = {
  [K in keyof Tool]?: never
}
