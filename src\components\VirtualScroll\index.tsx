import type { CSSProperties, ReactNode } from 'react'

import { onMounted } from '@/hooks'
import { rafThrottle } from '@/utils'
import { useUpdateEffect } from 'ahooks'
import classnames from 'clsx'
import { LoadingIcon } from '../Loading/LoadingIcon'

function InnerVirtualScroll<T>({
  style,
  className,
  contentClassName,
  contentStyle,

  data,
  itemHeight = 40,
  keyField,
  prev = 6,
  next = 6,

  loadMore,
  hasMore,
  showLoading,
  immediateLoad = false,
  children,
}: VirtualScrollProps<T>) {
  const refScroller = useRef<HTMLDivElement>(null)

  const [itemPool, setItemPool] = useState<number[]>([])

  const [isLoading, setIsLoading] = useState(false)
  const [isPending, setIsPending] = useState(false)

  const [stIndex, setStIndex] = useState(0)
  const totalHeight = itemHeight * data.length

  const setPool = rafThrottle(() => {
    const top = refScroller.current!.scrollTop
    const parentHeight = refScroller.current!.offsetHeight

    const isExceeded = top + parentHeight >= totalHeight - 6
    if (
      isExceeded
      && loadMore && hasMore
      && !isPending && !isLoading
    ) {
      setIsPending(true)
      setIsLoading(true)

      loadMore().finally(() => {
        setIsLoading(false)
        setIsPending(false)
      })
    }

    const stVal = Math.floor(top / itemHeight)
    const endIndex = Math.ceil((top + parentHeight) / itemHeight) + next

    const newStIndex = stVal - prev < 0
      ? 0
      : stVal - prev

    setStIndex(newStIndex)
    const stPos = newStIndex * itemHeight

    setItemPool(
      data.slice(newStIndex, endIndex)
        .map((_, index) => stPos + index * itemHeight),
    )
  })

  useUpdateEffect(
    () => {
      setPool()
    },
    [
      data,
      itemHeight,
      totalHeight,
      prev,
      next,
      hasMore,
      loadMore,
    ],
  )

  onMounted(() => {
    immediateLoad && loadMore()
    setPool()
  })

  return (<div
    className={ classnames(
      'overflow-auto relative',
      className,
    ) }
    style={ style }
    ref={ refScroller }
    onScroll={ setPool as any }
  >

    <div
      style={ {
        height: totalHeight,
        position: 'relative',
        ...contentStyle,
      } }
      className={ contentClassName }
    >
      { itemPool.map((height, index) =>
        <div
          key={ keyField
            ? (data as any)[stIndex + index][keyField]
            : index }
          className="absolute left-0 top-0 w-full"
          style={ {
            height: itemHeight,
            transform: `translate3d(0, ${height}px, 0)`,
            willChange: 'transform',
          } }
        >
          { children(data[stIndex + index], stIndex + index) }
        </div>,
      )}

      <div className="absolute bottom-1 left-0 w-full">
        { isLoading && showLoading && <LoadingIcon size={ 30 } /> }
      </div>
    </div>

  </div>)
}
InnerVirtualScroll.displayName = 'VirtualScroll'

export const VirtualScroll = memo(InnerVirtualScroll) as typeof InnerVirtualScroll

export type VirtualScrollProps<T> = {
  className?: string
  style?: CSSProperties
  contentClassName?: string
  contentStyle?: CSSProperties

  data: T[]
  itemHeight?: number
  keyField?: string
  prev?: number
  next?: number

  loadMore: () => Promise<any>
  immediateLoad?: boolean
  hasMore?: boolean
  showLoading?: boolean
  children: (item: T, index: number) => ReactNode
}
