import clsx from 'clsx'
import { motion } from 'framer-motion'
import React, { useCallback } from 'react'

/**
 * Individual item within a dropdown menu
 */
export const NavbarDropdownItem = React.memo(
  ({ className, active = false, icon, children, onClick, style }: NavbarDropdownItemProps) => {
    const handleClick = useCallback(() => {
      if (onClick)
        onClick()
    }, [onClick])

    return (
      <motion.button
        className={ clsx(
          'w-full px-4 py-2 text-sm flex items-center gap-2',
          'transition-all duration-150 group',
          className,
        ) }
        onClick={ handleClick }
        whileTap={ { scale: 0.98 } }
        style={ style }
      >
        { icon && <span>{ icon }</span> }
        <span className="transition-all duration-300 group-hover:translate-x-2">{ children }</span>

        {/* Dot */ }
        { active && (
          <motion.span
            className="ml-auto h-1.5 w-1.5 rounded-full bg-current"
            layoutId="activeIndicator"
            transition={ { type: 'spring', stiffness: 300, damping: 30 } }
          />
        ) }
      </motion.button>
    )
  },
)

NavbarDropdownItem.displayName = 'NavbarDropdownItem'

export type NavbarDropdownItemProps = {
  className?: string
  active?: boolean
  icon?: React.ReactNode
  children?: React.ReactNode
  onClick?: () => void
  style?: React.CSSProperties
}
