import { mdToHTML } from '@/utils'
import { useAsyncEffect } from 'ahooks'
import { Skeleton } from 'antd'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { Btns } from './Btns'

export default function TextContent({ txt, className, onDownload, onEdit, disabled, noData }: TextContentProps) {
  const [md, setMd] = useState('')
  useAsyncEffect(
    async () => {
      const md = await mdToHTML(txt)
      setMd(md)
    },
    [txt],
  )

  return (
    <motion.div
      initial={ { opacity: 0 } }
      animate={ { opacity: 1 } }
      exit={ { opacity: 0 } }
      transition={ { duration: 0.3 } }
      className={ clsx('text-content-container h-full animate-fade-in-right relative', className) }
      style={ {
        animationTimingFunction: 'ease-in-out',
        animationDuration: '.6s',
      } }
    >
      <Btns
        onDonwload={ onDownload }
        onEdit={ onEdit }
        disabled={ disabled }
      />

      <div className="markdown-body h-full flex flex-col overflow-auto space-y-6">
        { !md && !noData && <div className="relative h-96 overflow-hidden">
          <div className="loadingText mb-8 text-gray-400">Deep Searching</div>
          <Skeleton active />
          <Skeleton active />
        </div> }

        {
          noData && <motion.div
            initial={ { opacity: 0 } }
            animate={ { opacity: 1 } }
            transition={ { duration: 0.5 } }
            className="h-full flex items-center justify-center"
          >
            <div className="text-center text-gray-400">
              <motion.div
                initial={ { scale: 0.5 } }
                animate={ { scale: 1 } }
                transition={ {
                  type: 'spring',
                  stiffness: 260,
                  damping: 20,
                } }
                className="mb-2 text-4xl"
              >
                📊
              </motion.div>
              <motion.div
                initial={ { opacity: 0 } }
                animate={ { opacity: 1 } }
                transition={ { delay: 0.3 } }
              >
                No Data
              </motion.div>
            </div>
          </motion.div>
        }

        <div dangerouslySetInnerHTML={ { __html: md } } className="h-full overflow-auto"></div>
      </div>
    </motion.div>
  )
}

TextContent.displayName = 'TextContent'

export type TextContentProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  txt: string
  onDownload?: () => void
  onEdit?: () => void
  noData?: boolean
  disabled?: boolean
}
