import type { ShowMode } from '../store'
import { motion } from 'framer-motion'
import { titleMap } from '../constants'
import { chatStore } from '../store'
import { iconMap } from './Icons'

export const Switch: React.FC = () => {
  const snap = chatStore.use()
  const items = Object.values(snap.selectedMode).map(item => ({
    key: item,
    title: titleMap()[item as ShowMode],
  }))

  return (
    <nav className="switch-container h-full flex gap-6">
      { items.map((tab) => {
        const isActive = snap.showMode === tab.key
        return (
          <button
            key={ tab.key }
            onClick={ () => chatStore.showMode = tab.key }
            className={ `relative flex items-center gap-2 font-medium transition-colors duration-200 ease-in-out focus:outline-none hover:text-gray-800 hover:text-gray-800 group
              ${isActive
            ? 'text-gray-800'
            : 'text-gray-400'}` }
            aria-selected={ isActive }
            role="tab"
          >
            {/* Icon Container */ }
            <div className={ `flex items-center justify-center rounded-full p-[6px] transition-colors duration-200 ease-in-out group-hover:bg-gray-200 group-hover:text-gray-800
            ${isActive
            ? 'bg-gray-200 text-gray-800'
            : 'bg-gray-100 text-gray-400'}` }>

              { iconMap[tab.key] }
            </div>

            {/* Label Wrapper - Added relative positioning here */ }
            <div className="relative px-1">
              { tab.title }

              {/* Active Underline - Now positioned relative to the text label */ }
              { isActive && (
                <motion.div
                  className="absolute bottom-[-5px] left-0 right-0 h-[2px] bg-gray-600" // Adjust bottom offset, thickness, color
                  layoutId="underline" // Key for smooth animation
                  transition={ { type: 'spring', stiffness: 380, damping: 30 } } // Customize animation
                />
              ) }
            </div>
          </button>
        )
      }) }
    </nav>
  )
}
