/* eslint-disable react-hooks/rules-of-hooks */

export function useRefresh() {
  const [_, refresh] = useState({})
  return useCallback(() => refresh({}), [])
}

export function onMounted(fn: EffectFn) {
  return useEffect(fn, [])
}

export function onUnmounted(fn: ReturnType<EffectFn>) {
  return useEffect(() => {
    return fn
  }, [])
}

export function useUpdateEffect(
  fn: EffectFn,
  deps: any[] = [],
  options: EffectOpts = {},
) {
  const { effectFn = useEffect } = options
  const isFirstRender = useRef(true)

  return effectFn(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false
      return
    }

    return fn()
  }, deps)
}

export function useAsyncEffect(
  fn: () => Promise<any>,
  deps: any[] = [],
  options: EffectOpts & { onlyRunInUpdate?: boolean } = {},
) {
  const {
    onlyRunInUpdate = false,
    effectFn = useEffect,
  } = options
  const isFirstRender = useRef(true)

  return effectFn(() => {
    if (isFirstRender.current && onlyRunInUpdate) {
      isFirstRender.current = false
      return
    }

    const clean = fn()

    return () => {
      clean.then((fn) => {
        fn?.()
      })
    }
  }, deps)
}

type EffectFn = Parameters<typeof useEffect>[0]

type EffectOpts = {
  effectFn?: typeof useEffect
}
