import type { NavbarItemProps } from './NavbarItem'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { NavbarDropdownItem } from './NavbarDropdownItem'
import { NavbarItem } from './NavbarItem'

const Navbar = memo(({
  className,
  children,
  style,
  items,
  activeItem,
  onItemClick,
  dropdownRenderer,
}: NavbarProps) => {
  const isParentActive = useCallback(
    (item: NavItem) => {
      if (activeItem === item.id)
        return true
      if (item.dropdownItems?.some(subItem => activeItem === subItem.id))
        return true
      return false
    },
    [activeItem],
  )

  return (
    <motion.nav
      initial={ { y: -20, opacity: 0 } }
      animate={ { y: 0, opacity: 1 } }
      transition={ { duration: 0.4, ease: 'easeOut' } }
      className={ clsx('flex items-center justify-center', className) }
      style={ style }
    >
      <ul className="flex items-center gap-8">
        { items
          ? items.map(item => (
              <NavbarItem
                item={ item }
                dropdownRenderer={ dropdownRenderer }
                key={ item.id }
                active={ isParentActive(item) }
                hasDropdown={ !!item.dropdownItems?.length }
                onClick={ () => onItemClick?.(item.id) }
                className={ item.className }
                dropdownContent={
                  item.dropdownItems?.length
                    ? (
                        <>
                          { item.dropdownItems.map(dropdownItem => (
                            <NavbarDropdownItem
                              key={ dropdownItem.id }
                              icon={ dropdownItem.icon }
                              active={ activeItem === dropdownItem.id }
                              onClick={ () => onItemClick?.(dropdownItem.id) }
                              className={ dropdownItem.className }
                            >
                              { dropdownItem.label }
                            </NavbarDropdownItem>
                          )) }
                        </>
                      )
                    : undefined
                }
              >
                { item.icon && <span className="mr-1">{ item.icon }</span> }
                { item.label }
              </NavbarItem>
            ))
          : children }
      </ul>
    </motion.nav>
  )
})

Navbar.displayName = 'Navbar'

export type NavItem = {
  id: string
  label: React.ReactNode
  icon?: React.ReactNode
  className?: string
  dropdownItems?: {
    id: string
    label: React.ReactNode
    icon?: React.ReactNode
    className?: string
  }[]
}

export type NavbarProps = {
  className?: string
  brand?: React.ReactNode
  children?: React.ReactNode
  style?: React.CSSProperties
  items?: NavItem[]
  activeItem?: string
  onItemClick?: (itemId: string) => void

  dropdownRenderer?: NavbarItemProps['dropdownRenderer']
}

export { Navbar, NavbarDropdownItem, NavbarItem }
