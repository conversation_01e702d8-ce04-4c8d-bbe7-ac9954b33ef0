import type { UseReqOpts } from './types'
import { useCallback, useEffect, useState } from 'react'

export function useReq<T>(
  requestFn: () => Promise<T>,
  opts: UseReqOpts<T>,
) {
  const [loading, setLoading] = useState(opts.initLoading)
  const [data, setData] = useState<T | undefined>(opts.initData)
  const [error, setError] = useState<Error>()

  const request = async () => {
    setLoading(true)
    opts.setLoading?.(true)

    try {
      const data = await requestFn()
      setData(data)
      opts.onSuccess?.(data)
    }
    catch (error) {
      setError(error as Error)
      opts.onError?.(error)
    }
    finally {
      setLoading(false)
      opts.setLoading?.(false)
      opts.onFinally?.()
    }
  }

  return {
    loading,
    data,
    error,
    request: useCallback(request, [requestFn]),
  }
}

export function useWatchReq<T>(
  requestFn: () => Promise<T>,
  watchDeps: any[] = [],
  opts: UseReqOpts<T>,
) {
  const {
    loading,
    data,
    error,
    request,
  } = useReq(requestFn, opts)

  useEffect(() => {
    request()
  }, watchDeps)

  return {
    loading,
    data,
    error,
    request,
  }
}
