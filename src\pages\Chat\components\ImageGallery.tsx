import type { Msg } from '../types'
import { FakeProgress } from '@/components/Progress'
import { downloadByUrl } from '@jl-org/tool'
import { chatStore } from '../store'
import { Btns } from './Btns'

function ImageGallery(
  {
    className,
  }: ImageGalleryProps,
) {
  const snap = chatStore.use()
  const previewsMsgs = snap.previewMsgs as Msg[]

  return (
    <div
      className={ `ImageGallery-container h-full flex flex-col animate-fade-in-right ${className}` }
      style={ {
        animationTimingFunction: 'ease-in-out',
        animationDuration: '.6s',
      } }
    >
      <div className="relative mb-4 w-full flex-1 overflow-hidden rounded-lg">
        <div className="h-full w-full flex items-center justify-center"
        >
          { snap.selectedMsg?.files[0]?.base64
            ? <img
                src={ snap.selectedMsg.files[0]?.base64 }
                alt="Product image"
                className="h-full w-full object-contain"
              />
            : <FakeProgress showText={ false } /> }
        </div>

        <Btns
          onDonwload={ () => {
            snap.selectedMsg && downloadByUrl(snap.selectedMsg.files[0]?.base64)
          } }
          onEdit={ () => {
            chatStore.showEditor = true
          } }
        />
      </div>

      <div className="flex gap-3 overflow-x-auto pb-2"
      >
        { previewsMsgs.map((item, index) => (
          <div
            key={ item.id }
            onClick={ () => chatStore.selectedMsg = item }
            className="relative w-[5.6rem] overflow-auto rounded-lg"
          >
            <div className="aspect-square">
              { item.files.length
                ? <>
                    <img
                      src={ item.files[0]?.base64 }
                      alt="Thumbnail"
                      className="size-24 cursor-pointer rounded-md bg-gray-100 object-cover transition-all duration-300 hover-opacity-50"
                    />
                    {/* <Btns
                      className="!right-2 !top-2 !gap-1"
                      btnClassName="!p-0"
                      iconClass="!size-3"
                      onDonwload={ () => {
                        item.files[0]?.base64 && downloadByUrl(item.files[0]?.base64)
                      } }
                      onEdit={ () => {
                        chatStore.selectedMsg = item
                        chatStore.showEditor = true
                      } }
                    /> */}
                  </>
                : <FakeProgress showText={ false } size={ 50 } className="rounded-md" /> }
            </div>
          </div>
        )) }
      </div>
    </div>
  )
}

export default memo(ImageGallery)

export type ImageGalleryProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
