import type { CSSProperties, HTMLAttributes } from 'react'
import type { RateType } from './constants'
import { useT } from '@/hooks'
import { prefix } from '@/refactor/Photog/AIDrawing/constants'
import { Popover } from 'antd'
import classnames from 'clsx'
import { ChevronDown } from 'lucide-react'
import { memo } from 'react'
import { Q } from '../Q'
import { defaultRateOptions } from './constants'
import { RateExtra } from './RateExtra'

function _Rate({
  className,
  innerClassName,
  style,
  rate,
  setRate,
  titleHeight = 20,
  ...rest
}: RateProps) {
  const t = useT()
  /**
   * 当选中的比例不在默认展示时
   */
  const isHide = useMemo(
    () => defaultRateOptions.includes(rate as any),
    [rate],
  )

  return (<div
    className={ classnames(
      'flex flex-col h-full gap-3',
      className,
    ) }
    style={ style }
    { ...rest }
  >
    <div
      className="flex justify-between"
      style={ {
        height: titleHeight,
      } }
    >
      <div className="flex gap-2">
        <p className="font-bold">{ t('photog.ratio') }</p>

        <Q
          placement="left"
          content={ <div className="max-w-80">
            Select the size of your image.
          </div> }
        />
      </div>

      <Popover
        content={ <RateExtra rate={ rate } setRate={ setRate } /> }
        className="cursor-pointer"
        placement="rightBottom"
        trigger="click"
      >
        <div className="flex items-center text-light transition-colors hover:text-black"
        >
          <span>
            { isHide
              ? rate
              : 'More' }
            &nbsp;
          </span>
          <ChevronDown />
        </div>
      </Popover>
    </div>

    <div
      className={ classnames(
        'flex items-center justify-between gap-2 bg-lightBg',
        innerClassName,
      ) }
      style={ {
        height: `calc(100% - ${titleHeight}px)`,
      } }
    >
      {
        defaultRateOptions.map((item, index) => (<div
          key={ index }
          className={ classnames(
            'text-center py-1 flex-1',
            'bg-innerBg cursor-pointer select-none',
            'transition duration-300',
            'hover:bg-white hover:text-primary hover:font-bold',
            rate === item
              ? 'text-primary bg-white font-bold'
              : 'bg-innerBg text-light',
          ) }
          onClick={ () => setRate(item) }
          data-id={ `${prefix}${item}` }
        >
          { item }
        </div>))
      }
    </div>
  </div>)
}
_Rate.displayName = 'Rate'

export const Rate = memo(_Rate)

export type RateProps = {
  className?: string
  innerClassName?: string
  style?: CSSProperties
  titleHeight?: number
  rate: RateType
  setRate: (rate: RateType) => void
}
& HTMLAttributes<HTMLDivElement>
