import cx from 'clsx'
import { memo } from 'react'

export const TipMode = memo<TipModeProps>((
  {
    style,
    className,
    active,
    onClick,
    children,
  },
) => {
  return <div
    className={ cx(
      'TipModeContainer flex border border-solid border-primary justify-center items-center gap-2 w-fit py-2 px-4 rounded-full text-black cursor-pointer text-sm',
      'hover:bg-primary hover:text-white transition-all duration-300 select-none',
      {
        'bg-primary text-white': active,
      },
      className,
    ) }
    style={ style }
    onClick={ onClick }
  >
    { children }
  </div>
})

TipMode.displayName = 'TipMode'

export type TipModeProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  active?: boolean
  onClick?: () => void
}
