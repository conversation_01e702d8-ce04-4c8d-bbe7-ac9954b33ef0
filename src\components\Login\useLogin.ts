import type { FormInstance } from 'antd'
import { API_LOGIN, API_USER } from '@/api'
import { clearKeepAlive } from '@/components/KeepAliveRoute'
import { isMobileDevice } from '@/config'
import { god } from '@/god'

import { useNavi, useT } from '@/hooks'
import { setTokenData, userStore } from '@/store/userStore'

export function useLogin(form: FormInstance<any>) {
  const t = useT()
  const to = useNavi()

  const [time, setTime] = useState(60)
  /** 是否可以发送验证码 */
  const [disabled, setDisabled] = useState(false)
  const [loading, setLoading] = useState(false)
  const timerRef = useRef<number>()

  useEffect(() => {
    return () => clearInterval(timerRef.current)
  }, [])

  /**
   * 监听time的变化
   */
  useEffect(() => {
    if (time === 0) {
      clearInterval(timerRef.current)
      setTime(60)
      setDisabled(false)
    }
  }, [time])

  /**
   * 发送验证码
   */
  const sendCode = async () => {
    try {
      const { email } = await form.validateFields(['email'])
      if (!email)
        return god.messageError('Please input your email!')
      if (disabled)
        return

      setDisabled(true)
      timerRef.current = window.setInterval(() => {
        setTime(pre => pre - 1)
      }, 1000)

      await API_LOGIN.getCaptchaMail({ email })
      god.messageSuccess(t('models.verification-code-sent-successfully'))
    }
    catch (error: any) {
      if (error.code === 500) {
        god.messageError(t('models.failed-to-send-the-verification-code'))
      }
    }
  }

  /**
   * 邮箱注册
   */
  const onEmailRegister = async ({
    password,
    email,
    captcha,
  }: {
    password: string
    email: string
    captcha: string
  }) => {
    emailRegister({
      password,
      email,
      captcha,
      onBeforeReq: () => setLoading(true),
      onAfterReq: () => setLoading(false),
      onSuccess: () => {
        god.messageSuccess(t('models.registered-successfully'))
      },
      onError: () => {
        god.messageError(t('models.account-has-been-registered'))
      },
    })
  }

  /**
   * 邮箱登录
   */
  const onEmailLogin = async (
    {
      password,
      email,
    }: {
      password: string
      email: string
    },
  ) => {
    emailLogin({
      password,
      email,
      onSuccess: () => to('/p/chat'),
      onAfterReq: () => setLoading(false),
      onBeforeReq: () => setLoading(true),
      onError: (error: any) => god.messageError(error?.msg || 'login failed'),
    })
  }

  /**
   * 忘记密码
   */
  const onForgotPwd = async (params: {
    email: string
    password: string
    captcha: string
    confirm?: string
  }) => {
    setLoading(true)
    delete params.confirm
    await API_USER.forgotPwd(params)
      .then(() => {
        god.messageSuccess('Password reset successfully')
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return {
    loading,
    /** 是否可以发送验证码 */
    disabled,
    time,

    onEmailLogin,
    onEmailRegister,
    onForgotPwd,
    sendCode,
  }
}

/**
 * 邮箱登录
 */
export async function emailLogin({
  password,
  email,
  onAfterReq,
  onBeforeReq,
  onSuccess,
  onError,
}: {
  password: string
  email: string
  onBeforeReq?: VoidFunction
  onAfterReq?: VoidFunction
  onSuccess?: VoidFunction
  onError?: (error: any) => void
}) {
  onBeforeReq?.()
  const loginParams = {
    email,
    password,
  }

  try {
    const loginRes = await API_USER.emailLogin(loginParams)
    setTokenData(loginRes.token)
    onSuccess?.()
  }
  catch (error: any) {
    onError?.(error)
  }
  finally {
    onAfterReq?.()
    clearKeepAlive()
    userStore.loginModal = false
  }
}

/**
 * 邮箱注册
 */
export async function emailRegister({
  password,
  email,
  captcha,
  onBeforeReq,
  onAfterReq,
  onSuccess,
  onError,
}: {
  password: string
  email: string
  captcha: string
  onBeforeReq?: VoidFunction
  onAfterReq?: VoidFunction
  onSuccess?: VoidFunction
  onError?: VoidFunction
}) {
  try {
    onBeforeReq?.()

    const registerReq = {
      email,
      password,
      captcha,
    }

    const res = await API_USER.emailRegister(registerReq)
    if (res) {
      onSuccess?.()
      if (isMobileDevice) {
        god.messageInfo('Registration successful! Please open the website on a desktop browser')
      }
      else {
        god.messageInfo('Registration successful!')
      }
    }
  }
  catch (error: any) {
    if (error.code === 500) {
      onError?.()
    }
  }
  finally {
    onAfterReq?.()
  }
}
