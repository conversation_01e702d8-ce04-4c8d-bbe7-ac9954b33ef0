import type { PopoverProps } from 'antd'
import { Popover } from 'antd'
import cn from 'clsx'
import { CircleHelp } from 'lucide-react'
import { memo } from 'react'

/**
 * 用户 QA 提示
 */
export const Q = memo((
  {
    style,
    className,
    ...rest
  }: QProps,
) => {
  return <Popover
    className={ cn(className) }
    style={ style }
    { ...rest }
  >
    <CircleHelp size={ 20 } className="cursor-pointer text-[#3267F0]" />
  </Popover>
})

Q.displayName = 'Q'

export type QProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
& PopoverProps
