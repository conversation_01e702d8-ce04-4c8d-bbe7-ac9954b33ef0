import { isMobile } from '@jl-org/tool'

export * from './unoTheme'

/**
 * 一次积分费用
 */
export const COST_FEE = 2
/**
 * 精准模式一次需要的积分
 */
export const PRECISE_COST_FEE = 6
/**
 * 高清放大一次的积分
 */
export const HIGH_QUALITY_COST_FEE = 50

/**
 * 训练一次需要的积分
 */
export const TRAIN_COST_FEE = 200

export const DEFAULT_MATERIAL_SIZE: [number, number] = [200, 200]

/**
 * 左侧边栏宽度
 */
export const LEFT_SIDER_WIDTH = 80

/**
 * 请求头约定的key
 */
export const REQUEST_HEADER_KEY = {
  AUTH: 'Authorization',
}

export const LOGIN_ENCRYPT_PUBLIC_KEY
  = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAM51dgYtMyF+tTQt80sfFOpSV27a7t9uaUVeFrdGiVxscuizE7H8SMntYqfn9lp8a5GH5P1/GGehVjUD2gF/4kcCAwEAAQ=='

/**
 * 判断是否生产环境
 */
export const IS_PROD = import.meta.env.MODE === 'production'
export const IS_DEV = import.meta.env.MODE === 'development'
export const IS_ZH = import.meta.env.MODE === 'zh'

export const CREDITS_COST = {
  TRAIN: 30,
  GENERATE_IMAGE: 8,
}

export const UV_MAX_FILE_COUNT = 50

export const isMobileDevice = isMobile()

export const RomanNumMap = {
  1: 'I',
  2: 'II',
  3: 'III',
  4: 'IV',
  5: 'V',
  6: 'VI',
  7: 'VII',
  8: 'VIII',
  9: 'IX',
  10: 'X',
}

export const THEME_KEY = 'theme'
