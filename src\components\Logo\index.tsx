import type { FC } from 'react'
import SvgIcon from '@/components/SvgIcon'
import cn from 'clsx'
import { IS_ZH } from '@/config'

const Logo: FC<Props> = (props) => {
  const {
    className,
    onClick,
    needGradient = true,
    logoColor,
  } = props

  function Gradient() {
    if (logoColor) {
      return <linearGradient id="paint0_linear_3409_3823" x1="-0.255331" y1="7.95994" x2="88.723" y2="7.95994" gradientUnits="userSpaceOnUse">
        <stop offset="0" stopColor={ logoColor } />
        <stop offset="1" stopColor={ logoColor } />
      </linearGradient>
    }
    else {
      return needGradient
        ? <linearGradient id="paint0_linear_3409_3823" x1="-0.255331" y1="7.95994" x2="88.723" y2="7.95994" gradientUnits="userSpaceOnUse">
          <stop offset="0" stopColor="#cecece" />
          <stop offset="1" stopColor="#212121" />
        </linearGradient>

        : <linearGradient id="paint0_linear_3409_3823" x1="-0.255331" y1="7.95994" x2="88.723" y2="7.95994" gradientUnits="userSpaceOnUse">
          <stop offset="0" stopColor="#fff" />
          <stop offset="1" stopColor="#fff" />
        </linearGradient>
    }
  }

  return (
    <div
      className={ cn('flex items-center m-0', className) }
      style={ 'onClick' in props
        ? { cursor: 'pointer' }
        : undefined }
      onClick={ onClick }
    >
      {
        IS_ZH
          ? <img
            src={ new URL('@/assets/image/logo-picball.webp', import.meta.url).href }
            className="w-18"
          />

          : <>
            <SvgIcon icon="logo-photog" />
            <svg width="100" height="20" viewBox="0 0 89 16" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" clipRule="evenodd" d="M32.3407 14.388C33.4554 15.0627 34.7241 15.4 36.1467 15.4C37.5694 15.4 38.8307 15.0627 39.9307 14.388C41.0307 13.7133 41.8814 12.7893 42.4827 11.616C43.0841 10.4427 43.3847 9.13733 43.3847 7.7C43.3847 6.26267 43.0841 4.95733 42.4827 3.784C41.8814 2.61067 41.0307 1.68667 39.9307 1.012C38.8307 0.337333 37.5694 0 36.1467 0C34.7241 0 33.4554 0.337333 32.3407 1.012C31.2407 1.68667 30.3901 2.61067 29.7887 3.784C29.1874 4.95733 28.8867 6.26267 28.8867 7.7C28.8867 9.13733 29.1874 10.4427 29.7887 11.616C30.3901 12.7893 31.2407 13.7133 32.3407 14.388ZM39.2487 12.672C38.5154 13.8307 37.4814 14.41 36.1467 14.41C34.8121 14.41 33.7781 13.8307 33.0447 12.672C32.3261 11.4987 31.9667 9.84133 31.9667 7.7C31.9667 5.55867 32.3261 3.90867 33.0447 2.75C33.7781 1.57667 34.8121 0.989999 36.1467 0.989999C37.4814 0.989999 38.5154 1.57667 39.2487 2.75C39.9821 3.90867 40.3487 5.55867 40.3487 7.7C40.3487 9.84133 39.9821 11.4987 39.2487 12.672ZM60.247 14.388C61.3616 15.0627 62.6303 15.4 64.053 15.4C65.4756 15.4 66.737 15.0627 67.837 14.388C68.937 13.7133 69.7876 12.7893 70.389 11.616C70.9903 10.4427 71.291 9.13733 71.291 7.7C71.291 6.26267 70.9903 4.95733 70.389 3.784C69.7876 2.61067 68.937 1.68667 67.837 1.012C66.737 0.337333 65.4756 0 64.053 0C62.6303 0 61.3616 0.337333 60.247 1.012C59.147 1.68667 58.2963 2.61067 57.695 3.784C57.0936 4.95733 56.793 6.26267 56.793 7.7C56.793 9.13733 57.0936 10.4427 57.695 11.616C58.2963 12.7893 59.147 13.7133 60.247 14.388ZM67.155 12.672C66.4216 13.8307 65.3876 14.41 64.053 14.41C62.7183 14.41 61.6843 13.8307 60.951 12.672C60.2323 11.4987 59.873 9.84133 59.873 7.7C59.873 5.55867 60.2323 3.90867 60.951 2.75C61.6843 1.57667 62.7183 0.989999 64.053 0.989999C65.3876 0.989999 66.4216 1.57667 67.155 2.75C67.8883 3.90867 68.255 5.55867 68.255 7.7C68.255 9.84133 67.8883 11.4987 67.155 12.672ZM76.7367 14.366C77.9541 15.0553 79.3107 15.4 80.8067 15.4C81.9507 15.4 82.9994 15.2827 83.9527 15.048C84.9061 14.8133 85.8007 14.4393 86.6367 13.926V9.988C86.6367 8.59467 87.1061 7.898 88.0447 7.898V7.348C86.3727 7.348 85.2434 7.60467 84.6567 8.118C84.0701 8.61667 83.7767 9.25467 83.7767 10.032V13.948C82.9407 14.3293 81.9507 14.52 80.8067 14.52C79.3254 14.52 78.1447 13.9773 77.2647 12.892C76.3994 11.792 75.9667 9.988 75.9667 7.48C75.9667 5.61733 76.3627 4.07 77.1547 2.838C77.9467 1.606 79.1641 0.989999 80.8067 0.989999C82.0974 0.989999 83.1094 1.298 83.8427 1.914C84.5907 2.53 85.1041 3.50533 85.3827 4.84H86.0427C86.0427 4.12133 86.0061 3.51267 85.9327 3.014C85.8741 2.51533 85.7201 2.10467 85.4707 1.782C85.0307 1.26867 84.3561 0.843333 83.4467 0.506C82.5374 0.168667 81.4374 0 80.1467 0C78.7241 0 77.4554 0.329999 76.3407 0.989999C75.2407 1.63533 74.3901 2.53 73.7887 3.674C73.1874 4.80333 72.8867 6.072 72.8867 7.48C72.8867 8.94667 73.2241 10.2887 73.8987 11.506C74.5881 12.7087 75.5341 13.662 76.7367 14.366ZM49.3106 1.42994H47.7486C46.9273 1.42994 46.2966 1.63527 45.8566 2.04594C45.4313 2.45661 45.2186 3.13127 45.2186 4.06994H44.5586V2.19994C44.5586 1.02661 45.1819 0.439941 46.4286 0.439941H56.7686V1.42994H52.1706V14.9599H49.3106V1.42994ZM14.4553 3.29994C14.4553 2.55194 14.3526 2.00194 14.1473 1.64994C13.942 1.28327 13.5313 1.09994 12.9153 1.09994H12.6953V0.439941H15.3353C15.8633 0.439941 16.2666 0.527941 16.5453 0.703942C16.824 0.879941 17.022 1.17327 17.1393 1.58394C17.2566 1.97994 17.3153 2.55194 17.3153 3.29994V6.92994H23.2553V0.439941H26.1153V12.0999C26.1153 12.8479 26.218 13.4053 26.4233 13.7719C26.6286 14.1239 27.0393 14.2999 27.6553 14.2999H27.8753V14.9599H25.2353C24.7073 14.9599 24.304 14.8793 24.0253 14.7179C23.7466 14.5419 23.5486 14.2559 23.4313 13.8599C23.314 13.4493 23.2553 12.8626 23.2553 12.0999V7.91994H17.3153V14.9599H14.4553V3.29994ZM1.452 1.64994C1.65733 2.00194 1.76 2.55194 1.76 3.29994V14.9599H4.62V9.08594H6.71C7.37 9.08594 8.096 8.98328 8.888 8.77794C9.68 8.57261 10.3913 8.15461 11.022 7.52394C11.6673 6.89327 11.99 6.00594 11.99 4.86194C11.99 3.10194 11.374 1.92861 10.142 1.34194C8.92467 0.740608 7.56067 0.439941 6.05 0.439941H0V1.09994H0.22C0.836 1.09994 1.24667 1.28327 1.452 1.64994ZM8.074 7.41394C7.51667 7.94194 6.80533 8.20594 5.94 8.20594H4.62V1.47394L5.06 1.40794C5.29467 1.34927 5.62467 1.31994 6.05 1.31994C6.85667 1.31994 7.53133 1.58394 8.074 2.11194C8.63133 2.63994 8.91 3.51994 8.91 4.75194C8.91 5.99861 8.63133 6.88594 8.074 7.41394Z" fill="url(#paint0_linear_3409_3823)" />
              <defs>
                <Gradient></Gradient>
              </defs>
            </svg>
          </>
      }

    </div>
  )
}

export default memo(Logo)

interface Props {
  className?: string
  fromColor?: string
  toColor?: string
  onClick?: () => void

  logoColor?: string
  needGradient?: boolean
}
