import { cn } from '@/utils'
import { memo } from 'react'
import styles from './styles.module.scss'

export const Skeleton = memo<SkeletonProps>(({
  className,
  style,
  active = true,
  baseColor = '#e2e2e2',
  highlightColor = '#999999',
  animationDuration = 1,
  children,
  ...props
}) => {
  return (
    <div
      className={ cn(
        'animate-skeleton',
        styles.skeleton,
        className,
      ) }
      style={ {
        ...(active && {
          backgroundSize: '400%',
          backgroundImage: `linear-gradient(to right,
            ${baseColor} 0, ${baseColor} 30%,
            ${highlightColor} 45%, ${highlightColor} 50%,
            ${baseColor} 60%, ${baseColor})`,
          animationDuration: `${animationDuration}s`,
        }),
        ...style,
      } }
      { ...props }
    >
      { children }
    </div>
  )
})

export type SkeletonProps = {
  className?: string
  style?: React.CSSProperties
  /**
   * 是否激活
   * @default true
   */
  active?: boolean
  /**
   * 基础颜色
   * @default #e2e2e2
   */
  baseColor?: string
  /**
   * 高亮颜色
   * @default #999999
   */
  highlightColor?: string
  /**
   * 动画持续时间（秒）
   * @default 1
   */
  animationDuration?: number
  children?: React.ReactNode
} & React.HTMLAttributes<HTMLDivElement>
