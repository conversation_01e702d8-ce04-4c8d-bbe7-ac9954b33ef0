import type { CSSProperties } from 'react'
import type { SetStateParam } from './types'
import { debounce, isBrowser, isFn, throttle } from '@jl-org/tool'
import { useEffect, useMemo, useRef, useState } from 'react'
import { flushSync } from 'react-dom'

export function useToggle(initState = false) {
  const [state, setState] = useState(initState)
  const toggle = (val?: boolean) => {
    if (val != undefined) {
      setState(val)
      return
    }

    setState(val => !val)
  }

  return [state, toggle] as const
}

export function useThrottle<T>(initState: T, delayMS: number = 500) {
  const [state, _setState] = useState<T>(initState)
  const setState = (useMemo(
    () => {
      return throttle(_setState, delayMS)
    },
    [delayMS],
  )
  ) as unknown as typeof _setState

  return [
    state,
    setState,
  ] as const
}

export function useDebounce<T>(initState: T, delayMS: number = 500) {
  const [state, _setState] = useState<T>(initState)
  const setState = (useMemo(
    () => {
      return debounce(_setState, delayMS)
    },
    [delayMS],
  )
  ) as unknown as typeof _setState

  return [
    state,
    setState,
  ] as const
}

export function useWatchDebounce<T>(value: T, delayMS: number = 100) {
  const [state, setState] = useDebounce<T>(value, delayMS)
  useEffect(
    () => {
      setState(value)
    },
    [value, setState],
  )
  return state
}

export function useWatchThrottle<T>(value: T, delayMS: number = 100, options: UseWatchThrottleOptions = {}) {
  const {
    enable = true,
    syncLastValueTime = 1000,
  } = options

  const actualSyncLastValueTime = Math.max(syncLastValueTime, delayMS + 2)
  const timerRef = useRef<number>()
  const [state, setState] = useThrottle<T>(value, delayMS)

  useEffect(
    () => {
      if (enable) {
        setState(value)
      }

      timerRef.current = window.setTimeout(() => {
        setState(value)
      }, actualSyncLastValueTime)

      return () => clearTimeout(timerRef.current)
    },
    [value, setState, enable, actualSyncLastValueTime],
  )

  return enable
    ? state
    : value
}

export function vShow(
  show: boolean,
  opts: { visibility?: boolean } = {},
): CSSProperties {
  if (opts.visibility) {
    return show
      ? { visibility: 'visible' }
      /**
       * 不显示元素，大小拉满，但不占位置
       * 适用于隐藏元素，但不影响布局计算情况
       */
      : {
          visibility: 'hidden',
          position: 'absolute',
          zIndex: -99,
          width: '100%',
          height: '100%',
          top: 0,
          left: 0,
        }
  }

  return show
    ? {}
    : { display: 'none' }
}

export function useConst<T>(value: T | (() => T)) {
  const refValue = useRef<T>(
    isFn(value)
      ? value()
      : value,
  )
  return refValue.current
}

export function useWatchRef<T>(state: T) {
  const stateRef = useRef(state)
  useEffect(() => {
    stateRef.current = state
  }, [state])

  return stateRef
}

const isViewTransitionSupported = isBrowser && !!document.startViewTransition
export function useViewTransitionState<T>(initState: T | (() => T)) {
  const [state, setState] = useState<T>(initState)

  const setTransiton = (val: SetStateParam<T>) => {
    if (!isViewTransitionSupported) {
      setState(val)
      return
    }

    document.startViewTransition(() => {
      flushSync(() => setState(val))
    })
  }

  return [state, setTransiton] as const
}

export type UseWatchThrottleOptions = {
  enable?: boolean
  syncLastValueTime?: number
}
