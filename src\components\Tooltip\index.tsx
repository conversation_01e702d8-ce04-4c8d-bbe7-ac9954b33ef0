import { useResizeObserver } from '@/hooks'
import { cn } from '@/utils/tool'
import { motion } from 'framer-motion'
import { memo, useCallback, useEffect, useRef, useState } from 'react'

export const Tooltip = memo<TooltipProps>((props) => {
  const {
    children,
    content,
    placement = 'top',
    visible,
    trigger = 'hover',
    disabled = false,
    offset = 8,
    theme = 'dark',
    className,
    contentClassName,
    arrow = false,
    formatter,
    delay = 0,
    autoHideOnResize = false,
    ...rest
  } = props

  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()
  const resizeInitializedRef = useRef(false)

  const shouldShow = visible !== undefined
    ? visible
    : isVisible

  const calculatePosition = useCallback(() => {
    if (!triggerRef.current || !tooltipRef.current)
      return

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    }

    let x = 0
    let y = 0

    switch (placement) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.top - tooltipRect.height - offset
        break
      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.bottom + offset
        break
      case 'left':
        x = triggerRect.left - tooltipRect.width - offset
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
      case 'right':
        x = triggerRect.right + offset
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
    }

    if (x < 0)
      x = 8
    if (x + tooltipRect.width > viewport.width)
      x = viewport.width - tooltipRect.width - 8
    if (y < 0)
      y = 8
    if (y + tooltipRect.height > viewport.height)
      y = viewport.height - tooltipRect.height - 8

    setPosition({ x, y })
  }, [placement, offset])

  const showTooltip = () => {
    if (disabled)
      return

    if (delay > 0) {
      timeoutRef.current = setTimeout(() => {
        setIsVisible(true)
      }, delay)
    }
    else {
      setIsVisible(true)
    }
  }

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsVisible(false)
  }

  useResizeObserver(
    [triggerRef],
    () => {
      if (!resizeInitializedRef.current) {
        resizeInitializedRef.current = true
        return
      }

      if (autoHideOnResize && isVisible) {
        hideTooltip()
      }
    },
  )

  const handleMouseEnter = () => {
    if (trigger === 'hover' || trigger === 'focus') {
      showTooltip()
    }
  }

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      hideTooltip()
    }
  }

  const handleFocus = () => {
    if (trigger === 'focus') {
      showTooltip()
    }
  }

  const handleBlur = () => {
    if (trigger === 'focus') {
      hideTooltip()
    }
  }

  const handleClick = () => {
    if (trigger === 'click') {
      if (isVisible) {
        hideTooltip()
      }
      else {
        showTooltip()
      }
    }
  }

  useEffect(() => {
    if (shouldShow) {
      calculatePosition()

      const handleResize = () => calculatePosition()
      const handleScroll = () => calculatePosition()

      window.addEventListener('resize', handleResize)
      window.addEventListener('scroll', handleScroll, true)

      return () => {
        window.removeEventListener('resize', handleResize)
        window.removeEventListener('scroll', handleScroll, true)
      }
    }
  }, [shouldShow, calculatePosition])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const getArrowStyle = () => {
    const arrowSize = 6

    switch (placement) {
      case 'top':
        return {
          bottom: -arrowSize,
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderTop: `${arrowSize}px solid ${theme === 'dark'
            ? '#374151'
            : '#ffffff'}`,
        }
      case 'bottom':
        return {
          top: -arrowSize,
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid ${theme === 'dark'
            ? '#374151'
            : '#ffffff'}`,
        }
      case 'left':
        return {
          right: -arrowSize,
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderLeft: `${arrowSize}px solid ${theme === 'dark'
            ? '#374151'
            : '#ffffff'}`,
        }
      case 'right':
        return {
          left: -arrowSize,
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid ${theme === 'dark'
            ? '#374151'
            : '#ffffff'}`,
        }
      default:
        return {}
    }
  }

  const formattedContent = formatter && typeof content === 'number'
    ? formatter(content)
    : content

  return (
    <>
      <div
        ref={ triggerRef }
        className={ cn('inline-block', className) }
        onMouseEnter={ handleMouseEnter }
        onMouseLeave={ handleMouseLeave }
        onFocus={ handleFocus }
        onBlur={ handleBlur }
        onClick={ handleClick }
        { ...rest }
      >
        { children }
      </div>

      { shouldShow && formattedContent && (
        <motion.div
          ref={ tooltipRef }
          initial={ { opacity: 0, scale: 0.8 } }
          animate={ { opacity: 1, scale: 1 } }
          exit={ { opacity: 0, scale: 0.8 } }
          transition={ { duration: 0.15 } }
          className={ cn(
            'fixed z-50 px-2 py-1 text-xs rounded-lg shadow-lg pointer-events-none whitespace-nowrap',
            theme === 'dark'
              ? 'bg-black/70 text-white'
              : 'bg-white/70 text-gray-900',
            contentClassName,
          ) }
          style={ {
            left: position.x,
            top: position.y,
          } }
        >
          { formattedContent }

          { arrow && (
            <div
              className="absolute h-0 w-0"
              style={ getArrowStyle() }
            />
          ) }
        </motion.div>
      ) }
    </>
  )
})

Tooltip.displayName = 'Tooltip'

export type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right'
export type TooltipTrigger = 'hover' | 'focus' | 'click'
export type TooltipTheme = 'dark' | 'light'

export type TooltipProps = {
  children: React.ReactNode
  content?: React.ReactNode
  placement?: TooltipPlacement
  visible?: boolean
  trigger?: TooltipTrigger
  disabled?: boolean
  offset?: number
  className?: string
  contentClassName?: string
  arrow?: boolean
  theme?: TooltipTheme
  formatter?: (value: number) => React.ReactNode
  delay?: number
  autoHideOnResize?: boolean
} & Omit<React.HTMLAttributes<HTMLDivElement>, 'content'>
