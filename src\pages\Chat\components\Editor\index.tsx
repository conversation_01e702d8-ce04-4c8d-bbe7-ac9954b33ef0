import type { <PERSON>Item, OldUploaderRef } from '@/components/Uploader'
import type { Canvas, Textbox } from 'fabric'
import type { EditorRef } from './Editor'
import { UploaderOld } from '@/components/Uploader'
import { useMemoFn } from '@/hooks'
import { addImg, addText, createUnReDoList, delSelected, drawBgImg, enableDraw, exportJson, handleTextSelection, listenTextSelection, loadJson } from '@/utils'
import { motion } from 'framer-motion'
import { editorMultiplier, showEditorHeight } from '../../constants'
import { chatStore } from '../../store'
import { restorePoster } from '../../tool'
import { Editor } from './Editor'
import { Toolbar } from './Toolbar'

function App() {
  const fabricRef = useRef<Canvas | null>(null)
  const editorRef = useRef<EditorRef>(null)
  const imgMode = useRef<'img' | 'bg'>('img')
  const lastTextRef = useRef<Textbox | null>(null)

  const unRedoRef = useRef(createUnReDoList<string>())
  const needRecord = useRef(true)

  const [currentColor, setCurrentColor] = useState('#ffffff')
  console.log(currentColor)
  const [isDrawing, setIsDrawing] = useState(false)
  const uploaderRef = useRef<OldUploaderRef>(null)

  /***************************************************
   *                    Events
   ***************************************************/

  const handleImg = useMemoFn(() => {
    imgMode.current = 'img'
    uploaderRef.current?.click()
  })

  const handleBgImg = useMemoFn(() => {
    imgMode.current = 'bg'
    uploaderRef.current?.click()
  })

  const handleFileChange = useMemoFn(async (files: FileItem[]) => {
    if (!fabricRef.current || !files.length)
      return
    const file = files[0]

    imgMode.current === 'img'
      ? addImg(fabricRef.current, file.base64, { center: true, autoFit: true })
      : drawBgImg(fabricRef.current, file.base64, { center: true, autoFit: true, needClear: true })
  })

  const handleAddText = useCallback(() => {
    if (!fabricRef.current)
      return

    lastTextRef.current = addText(fabricRef.current, {
      fill: currentColor,
      getLastEl: () => lastTextRef.current,
      ...editorRef.current?.getPoint(),
    })
  }, [currentColor])

  const handleToggleDraw = useCallback(() => {
    if (!fabricRef.current)
      return

    setIsDrawing((isDrawing) => {
      if (!fabricRef.current)
        return isDrawing

      const res = !isDrawing
      res
        ? fabricRef.current!.isDrawingMode = true
        : fabricRef.current!.isDrawingMode = false

      res && enableDraw(fabricRef.current, brush => brush.color = currentColor)

      return res
    })
  }, [currentColor])

  const handleDownload = useMemoFn(() => {
    if (!fabricRef.current)
      return

    const dataUrl = fabricRef.current.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: editorMultiplier,
    })
    const link = document.createElement('a')
    link.download = 'canvas-image.png'
    link.href = dataUrl
    link.click()
  })

  const handleDelete = useMemoFn(() => {
    if (!fabricRef.current)
      return
    delSelected(fabricRef.current)
  })

  const changeNeedRecord = useMemoFn(() => {
    needRecord.current = false
    setTimeout(() => {
      needRecord.current = true
    }, 4)
  })

  const handleUndo = useMemoFn(() => {
    if (!fabricRef.current)
      return

    changeNeedRecord()
    unRedoRef.current.undo((json) => {
      if (json) {
        loadJson(fabricRef.current!, json)
        return
      }

      fabricRef.current!.clear()
      if (!chatStore.selectedMsg)
        return
      restorePoster(fabricRef.current!, chatStore.selectedMsg)
    })
  })

  const handleRedo = useMemoFn(() => {
    if (!fabricRef.current)
      return

    changeNeedRecord()
    unRedoRef.current.redo((json) => {
      json && loadJson(fabricRef.current!, json)
    })
  })

  /***************************************************
   *                    Watch
   ***************************************************/

  useEffect(
    () => {
      if (!fabricRef.current)
        return

      const canvas = fabricRef.current

      /**
       * 处理画笔颜色
       */
      if (canvas?.freeDrawingBrush) {
        canvas.freeDrawingBrush.color = currentColor
      }

      /**
       * 记录历史
       */
      const record = () => needRecord.current && unRedoRef.current.add(exportJson(canvas))
      canvas.on('object:added', record)
      canvas.on('object:modified', record)

      /**
       * 处理文本选择的颜色
       */
      handleTextSelection(canvas, currentColor, record)
      // const unbindTextSelection = listenTextSelection(canvas, currentColor, record)

      return () => {
        canvas.off('object:added', record)
        canvas.off('object:modified', record)
        // unbindTextSelection()
      }
    },
    [currentColor],
  )

  return (
    <motion.div
      initial={ { opacity: 0 } }
      animate={ { opacity: 1 } }
      className="flex gap-4"
      style={ { height: showEditorHeight + 20 } }
    >
      <UploaderOld
        ref={ uploaderRef }
        autoClear
        onChange={ handleFileChange }
        accept="image/*"
        className="hidden"
      />

      <Toolbar
        currentColor={ currentColor }
        setCurrentColor={ setCurrentColor }
        onImgUpload={ handleImg }
        onBgImgUpload={ handleBgImg }
        onAddText={ handleAddText }
        onToggleDraw={ handleToggleDraw }
        onDownload={ handleDownload }
        onDelete={ handleDelete }
        onRedo={ handleRedo }
        onUndo={ handleUndo }
        isDrawing={ isDrawing }
      />

      <Editor
        fabricRef={ fabricRef }
        ref={ editorRef }
        className="flex-1"
      />
    </motion.div>
  )
}

export default memo(App)
