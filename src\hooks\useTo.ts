import type { router } from '@/router'
import type { RoutePath } from '@/router/routes'
import { HEADER_ID } from '@/components/RootLayout/constants'
import { createPortal } from 'react-dom'

export function useNavi() {
  return useNavigate() as (
    path: RoutePath | number,
    opts?: Parameters<typeof router['navigate']>[1]
  ) => void
}

export function useToHeader(children: React.ReactNode) {
  const [container, setContainer] = useState<HTMLElement | null>(null)

  useLayoutEffect(() => {
    const headerDom = document.querySelector(`#${HEADER_ID}`)
    if (!headerDom)
      return
    setContainer(headerDom as HTMLElement)
  }, [])

  return container
    ? createPortal(children, container)
    : null
}
