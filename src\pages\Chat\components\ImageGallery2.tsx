import type { Msg } from '../types'
import { AnimateShow } from '@/components/Animate'
import { FakeProgress } from '@/components/Progress'
import { RetryImg } from '@/components/RetryImg'
import { god } from '@/god'
import { downloadByUrl } from '@jl-org/tool'
import cx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { memo } from 'react'
import { chatStore } from '../store'
import { Btns } from './Btns'
import { ChnageImgInput } from './ChnageImgInput'
import { EditorModal } from './EditorModal'

function ImageGallery2(
  {
    className,
    style,
  }: ImageGallery2Props,
) {
  const snap = chatStore.use()
  const previewsMsgs = snap.previewMsgs as Msg[]

  return (
    <div
      className={ cx(
        'ImageGallery2-container h-full flex animate-fade-in-right gap-4',
        className,
      ) }
      style={ {
        animationTimingFunction: 'ease-in-out',
        animationDuration: '.6s',
        ...style,
      } }
    >
      {/* 主图显示区域 */ }
      <div className="relative aspect-square flex-1 overflow-hidden rounded-lg ring-1 ring-gray-200"
      >
        <motion.div
          className="relative h-full w-full flex items-center justify-center"
          initial={ { opacity: 0 } }
          animate={ { opacity: 1 } }
          exit={ { opacity: 0 } }
          transition={ { duration: 0.3 } }
        >
          <Btns
            onDonwload={ () => {
              snap.selectedMsg && downloadByUrl(snap.selectedMsg.files[0]?.base64)
            } }
            onEdit={ () => {
              if (chatStore.changeImging) {
                god.messageWarn('Image generation in progress ...')
                return
              }
              setTimeout(() => {
                chatStore.showEditor = true
              }, 50)
            } }
          />

          { snap.selectedMsg?.files[0]?.base64
            ? <RetryImg
                src={ snap.selectedMsg.files[0]?.base64 }
                alt="Product image"
                className="aspect-square w-full object-contain"
              />
            : <FakeProgress showText={ false } /> }
        </motion.div>

        {/* <EditorModal
          open={ snap.showEditor }
          onCancel={ () => chatStore.showEditor = false }
        /> */}

        <AnimateShow show={ snap.showEditor }>
          <ChnageImgInput
            className="absolute bottom-4 left-1/2 w-[80%] transform rounded-lg bg-white p-2 shadow-lg -translate-x-1/2"
            onCancel={ () => chatStore.showEditor = false }
          />
        </AnimateShow>
      </div>

      {/* 右侧缩略图区域 */ }
      <div className="hide-scroll w-24 overflow-x-visible overflow-y-auto p-1 space-y-3">
        { previewsMsgs.map(item => (
          <motion.div
            key={ item.id }
            onClick={ () => chatStore.selectedMsg = item }
            className={ cx(
              'relative cursor-pointer rounded-lg overflow-hidden aspect-square',
              snap.selectedMsg?.id === item.id
                ? 'ring-1 ring-blue-500'
                : '',
            ) }
            whileHover={ { scale: 1.05 } }
            whileTap={ { scale: 0.98 } }
          >
            <div className="aspect-square">
              { item.files.length
                ? item.files[0]?.base64
                  ? <RetryImg
                      src={ item.files[0]?.base64 }
                      alt="Thumbnail"
                      className="size-24 rounded-md bg-gray-100 object-cover"
                    />
                  : <FakeProgress showText={ false } size={ 50 } className="rounded-md" />

                : <FakeProgress showText={ false } size={ 50 } className="rounded-md" /> }
            </div>
          </motion.div>
        )) }
      </div>
    </div>
  )
}

export default memo(ImageGallery2)

export type ImageGallery2Props = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
