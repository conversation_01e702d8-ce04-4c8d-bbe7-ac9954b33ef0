import type { CollectionState } from '@/api/squareAPI'
import { PageQuery, PagerList } from "../view"
import { TaskStatus } from "./task"


export type ResourceDO = {
  id: string
  createUserString: string
  createTime: string
  disabled: boolean
  name: string
  path: string
  previewUrl: string
  watermarkUrl: string
  url: string
  status: number
  source: string
  width: number
  height: number
  batchId: number
  materialId: number
  materialName: string
  historyId: number
  email: string
  brand: string
  designer: string
  coverImageRemoveBackgroundUrl: string
  price: number
  collectionState: CollectionState
}

export type ResourceState = {
  /** 列表数据 */
  pagerList: PagerList<ResourceDO>
  /** 列表loading */
  loading: boolean
  /** 列表query */
  query: PageQuery<{
    /** 排序条件,示例值(createTime,desc) */
    sort?: string
    /** ids */
    ids?: ResourceDO["id"][]
    /** 
     * - 1-相似生成，
     * - 2-多角度生成，
     * - 3-背景替换生成，
     * - 4-A-T生成，
     * - 5-图片扩展操作生成，
     * - 6-产品抠图，
     * - 7-（新）背景替换生成
     */
    resourceType?: 1 | 2 | 3 | 4 | 5 | 6 | 7
  }>
  /** 当前选中项 */
  checkedList: ResourceDO[]
}

export type ResourceFourDO = {
  /** id */
  id: string
  /** 名称 */
  name: string
  /** 路径 */
  path: string
  /** 创建时间 */
  createTime: string
  /** 创建人 */
  createUserString: string
  /** 图片url */
  url: string
  /** 状态 */
  status: TaskStatus
  /** 图片宽度 */
  width: number
  /** 图片高度 */
  height: number
  materialName?: string
  type: string | null
  previewUrl?: string
}

export type ResourceFourState = {
  /** 列表数据 */
  pagerList: PagerList<ResourceDO>
  /** 列表loading */
  loading: boolean
  /** 列表query */
  query: PageQuery<{
    /** 排序条件,示例值(createTime,desc) */
    resourceType: number
    sort?: string
    /** ids */
    ids?: ResourceDO["id"][]
  }>
  /** 当前选中项 */
  checkedList: ResourceDO[]
}
