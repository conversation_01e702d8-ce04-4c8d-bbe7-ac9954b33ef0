import { TreeDataNode } from "antd";

export interface CatalogState {
  /** list loading */
  loading: boolean;
  /** 目录树列表 */
  catalogTreeList: CatalogVO[];
  /** 添加文件夹弹窗 */
  addCatalogModalOpen: boolean;
  /** 添加目录参数 */
  addCatalogParams: AddRenameCatalogParams;
  /** 操作loading */
  actionLoading: boolean;
}

interface AddRenameCatalogParams {
  dirId?: CatalogDO["id"];
  name: string;
  /** isRename */
  isRename: boolean;
}

/** 目录对象 */
export interface CatalogDO {
  /** id 0表示一级目录 */
  id: string | 0;
  /** 目录名 */
  name: string;
  /** 子文件夹数量 */
  numOfSubDir: number;
  /** 子文件数量 */
  numOfChildren: number;
  /** 父级id 根目录则为null */
  parentDirectoryId: string | null;
  /** 创建时间 */
  createTime: string;
  /** 创建人 */
  createUserString: string;
  /** children */
  children?: CatalogDO[];
}

export interface CatalogVO extends TreeDataNode, CatalogDO {
  depth?: number;
  children?: CatalogVO[];
}
