import type { LogEntry } from '@/worker/parseAgent'
import clsx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion' // 导入 motion 和 AnimatePresence
import { memo } from 'react'

export type LogTheme = 'default' | 'terminal' | 'dark-terminal'

interface LogViewerProps {
  logs: readonly Log<PERSON>ntry[]
  className?: string
  /** 日志主题样式 */
  theme?: LogTheme
}

export const LogViewer = memo<LogViewerProps>(({ logs, className, theme = 'default' }) => {
  if (!logs || logs.length === 0) {
    return null
  }

  const getThemeStyles = () => {
    switch (theme) {
      case 'terminal':
        return {
          container: 'bg-black text-green-400 p-4 rounded-md font-mono',
          logEntry: 'border-b border-green-900 pb-2 mb-2 last:border-0 last:mb-0 last:pb-0',
          timestamp: 'text-green-600',
          levelBadge: {
            base: 'px-1.5 py-0.5 rounded-sm text-xs mr-2',
            INFO: 'bg-green-900 text-green-400',
            ERROR: 'bg-red-900 text-red-400',
            WARNING: 'bg-yellow-900 text-yellow-400',
            DEBUG: 'bg-blue-900 text-blue-400',
          },
          source: 'text-green-500 font-bold',
          message: 'text-green-300 mt-1',
        }
      case 'dark-terminal':
        return {
          container: 'bg-gray-900 text-blue-400 p-4 rounded-md font-mono',
          logEntry: 'border-b border-gray-800 pb-2 mb-2 last:border-0 last:mb-0 last:pb-0',
          timestamp: 'text-gray-500',
          levelBadge: {
            base: 'px-1.5 py-0.5 rounded-sm text-xs mr-2',
            INFO: 'bg-blue-900 text-blue-300',
            ERROR: 'bg-red-900 text-red-300',
            WARNING: 'bg-yellow-900 text-yellow-300',
            DEBUG: 'bg-gray-800 text-gray-300',
          },
          source: 'text-cyan-400 font-bold',
          message: 'text-gray-300 mt-1',
        }
      default:
        return {
          container: 'flex flex-col gap-1',
          logEntry: clsx(
            'p-2 rounded text-sm font-mono',
          ),
          timestamp: 'text-xs text-gray-500',
          levelBadge: {
            base: 'px-1.5 py-0.5 rounded-full text-xs',
            INFO: 'bg-blue-200 text-blue-800',
            ERROR: 'bg-red-200 text-red-800',
            WARNING: 'bg-yellow-200 text-yellow-800',
            DEBUG: 'bg-gray-200 text-gray-800',
          },
          source: 'text-xs font-semibold',
          message: 'mt-1 whitespace-pre-wrap',
        }
    }
  }

  const styles = getThemeStyles()

  const getTerminalPrefix = (entry: LogEntry) => {
    if (theme === 'terminal' || theme === 'dark-terminal') {
      return <span className="mr-1">
        { theme === 'terminal'
          ? '$ '
          : '> ' }
      </span>
    }
    return null
  }

  const getEntryBgColor = (level: string) => {
    if (theme !== 'default')
      return ''

    switch (level) {
      case 'INFO': return 'bg-blue-50 text-blue-800'
      case 'ERROR': return 'bg-red-50 text-red-800'
      case 'WARNING': return 'bg-yellow-50 text-yellow-800'
      case 'DEBUG': return 'bg-gray-50 text-gray-800'
      default: return ''
    }
  }

  /** 使用 AnimatePresence 包裹列表 */
  // initial={false} 避免组件首次挂载时所有元素都播放动画
  return (
    <div className={ clsx('log-viewer', styles.container, className) }>
      <AnimatePresence initial={ false }>
        { logs.map((entry, index) => (
          /**
           * 将每个日志条目的外层 div 替换为 motion.div
           * 确保 key 是稳定且唯一的，这里暂时用 timestamp 和 index 结合
           * 如果 LogEntry 有唯一 id，使用 id 更佳
           */
          <motion.div
            key={ `${entry.timestamp}-${index}` } //  需要稳定唯一的key
            className={ clsx(
              styles.logEntry,
              getEntryBgColor(entry.level),
            ) }
            style={ {
              transformOrigin: 'top', // 从顶部开始动画
            } }
            /** 定义入场动画 */
            initial={ { opacity: 0, y: 10, scaleY: 0 } } // 初始状态：透明，向下偏移10px
            animate={ { opacity: 1, y: 0, scaleY: 1 } } // 动画目标：完全不透明，回到原位
            transition={ { duration: 0.5, ease: 'easeOut' } } // 动画效果
            layout // 添加 layout 属性可以在元素增删时平滑调整布局（可选）
          >
            <div className="flex items-center gap-2">
              { getTerminalPrefix(entry) }
              <span className={ styles.timestamp }>{ entry.timestamp }</span>
              <span className={ clsx(
                styles.levelBadge.base,
                styles.levelBadge[entry.level as keyof typeof styles.levelBadge],
              ) }>
                { entry.level }
              </span>
              <span className={ styles.source }>{ entry.source }</span>
            </div>
            <div
              className={ styles.message }
            >
              { entry.message }
            </div>
          </motion.div>
        )) }
      </AnimatePresence>
    </div>
  )
})
