import { H<PERSON><PERSON>_KEY, HACK_VALUE } from '@/components/DTK/Hack'

import { IS_PROD } from '@/config'
import { disableRightClick, removeDevtoolsDetector } from '@/utils'
import { useEffect } from 'react'

/**
 * 禁用调试
 */
export function useAppInit() {
  useEffect(() => {
    const hackInfo = sessionStorage.getItem(HACK_KEY)

    const canHack = HACK_VALUE === hackInfo

    if (IS_PROD) {
      if (!canHack) {
        removeDevtoolsDetector()
      }

      disableRightClick()
    }
  }, [])
}
