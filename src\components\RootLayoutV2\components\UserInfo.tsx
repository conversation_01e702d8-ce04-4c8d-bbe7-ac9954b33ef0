import { TransparentModal } from '@/components/CloseModal/TransparentModal'
import SvgIcon from '@/components/SvgIcon'
import { UserProfileEdit } from '@/components/UserProfileEdit'
import { RechargeTypeEnum } from '@/dts'
import { useNavi, useT } from '@/hooks'
import { logout, userDetailStore } from '@/store/userStore'
import { cn } from '@/utils'
import cx from 'clsx'
import { memo, useState } from 'react'
import { useSnapshot } from 'valtio'

export const UserInfo = memo<UserInfoProps>((
  {
    style,
    className,
  },
) => {
  const t = useT()
  const to = useNavi()
  const userinfo = useSnapshot(userDetailStore)
  const hasAvatar = userinfo.avatar && userinfo.avatar !== ''

  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false)

  return <div
    className={ cx(
      'UserInfoContainer p-2 flex flex-col gap-8',
      className,
    ) }
    style={ style }
  >
    <div className="flex items-center gap-6">
      <div
        className={ cn(
          'relative size-16 flex cursor-pointer items-center justify-center overflow-hidden border border-gray-300 rounded-full border-solid bg-white transition-shadow hover:shadow-md',
          hasAvatar
            ? ''
            : 'p-1.2',
        ) }
        onClick={ () => setIsProfileModalOpen(true) }
      >
        <img
          src={ userinfo.avatar || new URL('@/assets/svg/avatar.svg', import.meta.url).href }
          alt=""
          className={ cn(
            {
              'size-full object-cover': hasAvatar,
            },
          ) }
        />
      </div>

      <div className="flex flex-col">
        <div className="text-lg font-bold">
          <span>Hi~</span>
          <span>{ ' ' }</span>
          <span>{ userinfo.nickname || userinfo.username }</span>
        </div>

        <div className="">{ userinfo.email }</div>
      </div>
    </div>

    <div className="flex gap-4">
      <div className="flex-1 bg-lightBg p-4">
        <div className="flex items-center gap-2">
          <div
            className={ cx(
              `flex gap-2 cursor-pointer bg-white rounded-full overflow-hidden`,
              'transition hover:bg-lightBg p-2',
            ) }
            onClick={ () => to('/pricing', { state: { isLoginMode: false, type: RechargeTypeEnum.MONTHLY } }) }
          >
            <SvgIcon icon="charge" noFill className="h-6 w-6"></SvgIcon>
          </div>
          <span className="truncate text-sm">{ userinfo.totalCredits }</span>
        </div>
      </div>

      <div className="relative bg-lightBg p-4">
        <div
          className={ cx(
            `flex gap-2 cursor-pointer bg-white rounded-full overflow-hidden`,
            'transition hover:bg-lightBg p-2',
          ) }
          onClick={ () => to('/pricing', { state: { isLoginMode: false } }) }
        >
          <SvgIcon icon="credit3" noFill className="h-6 w-6"></SvgIcon>
        </div>

        <img
          src={ new URL('@/assets/svg/credit2.svg', import.meta.url).href }
          alt=""
          className="absolute right-0 size-10 -top-6"
        />
      </div>
    </div>

    {/* 退出登录 */ }
    <div
      onClick={ () => logout().then(() => {
        to('/')
      }) }
      className={ `mt-auto flex gap-4 items-center cursor-pointer pl-2 py-4
        text-gray-500 text-lg relative
        hover:bg-lightBg duration-300 transition-all` }
    >
      <SvgIcon icon="logout" noFill />
      <div>{ t('photog.logout') }</div>

      <div className="absolute right-0 h-[1px] w-full bg-gray-200 -top-3"></div>
    </div>

    {/* 用户资料编辑弹窗 */ }
    <TransparentModal
      open={ isProfileModalOpen }
      onCancel={ () => setIsProfileModalOpen(false) }
      width={ 800 }
    >
      <UserProfileEdit onClose={ () => setIsProfileModalOpen(false) } />
    </TransparentModal>
  </div>
})

UserInfo.displayName = 'UserInfo'

export type UserInfoProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}
