import Logo from '@/components/Logo'
import SvgIcon from '@/components/SvgIcon'
import { RechargeTypeEnum } from '@/dts'
import { useNavi, useT } from '@/hooks'
import { userDetailStore } from '@/store/userStore'
import { Popover } from 'antd'
import cn from 'clsx'
import { motion } from 'framer-motion'
import React from 'react'
import { useSnapshot } from 'valtio'
import { UserInfo } from './UserInfo'

type NavItem = {
  name: string
  pathname: string
}

type NavbarPicBallProps = {
  className?: string
  style?: React.CSSProperties
}

const NavbarPicBall = React.memo(({ className, style }: NavbarPicBallProps) => {
  const to = useNavi()
  const t = useT()
  const userinfo = useSnapshot(userDetailStore)

  const navItems: NavItem[] = [
    { name: t('layout.nav-lab'), pathname: '/p/chat' },
    { name: t('layout.nav-agent'), pathname: '/p/agent' },
    { name: t('layout.nav-square'), pathname: '/p/history' },
    { name: t('layout.nav-photo'), pathname: '/p/photog' },
    { name: t('layout.nav-video'), pathname: '/p/video' },
    { name: t('layout.nav-assets'), pathname: '/p/assets' },
  ]

  return (
    <motion.header
      initial={ { y: -20, opacity: 0 } }
      animate={ { y: 0, opacity: 1 } }
      transition={ { duration: 0.5, ease: 'easeOut' } }
      className={ cn(
        'w-full px-4 bg-white relative pt-2',
        className,
      ) }
      style={ style }
    >
      <div className="w-full flex items-center justify-between rounded-md px-4 py-2 shadow-black/10 shadow-md">
        <Logo className="mr-12" logoColor="#333"></Logo>

        <nav className="absolute left-1/2 flex transform items-center gap-8 -translate-x-1/2">
          { navItems.map(item => (
            <NavLink key={ item.name } item={ item } />
          )) }
        </nav>

        <div className="flex items-center gap-4">
          <div
            className={ cn(
              `flex gap-2 cursor-pointer bg-white rounded-full overflow-hidden`,
              'transition hover:bg-innerBg p-2',
            ) }
            onClick={ () => to('/p/innerPricing', { state: { isLoginMode: false } }) }
          >
            <SvgIcon icon="credit3" noFill className="h-6 w-6"></SvgIcon>
          </div>

          <div className="flex items-center gap-2">
            <div
              className={ cn(
                `flex gap-2 cursor-pointer bg-white rounded-full overflow-hidden`,
                'transition hover:bg-innerBg p-2',
              ) }
              onClick={ () => to('/p/innerPricing', { state: { isLoginMode: false, type: RechargeTypeEnum.MONTHLY } }) }
            >
              <SvgIcon icon="charge" noFill className="h-6 w-6"></SvgIcon>
            </div>
            <span className="text-sm">{ userinfo.totalCredits }</span>
          </div>

          <Popover
            content={ <UserInfo></UserInfo> }
          >
            <div
              className="relative size-8 cursor-pointer border border-gray-200 rounded-full border-solid bg-white p-[6px]"
            >
              <img
                src={ new URL('@/assets/svg/avatar.svg', import.meta.url).href }
                alt=""
                className="w-full"
              />
            </div>
          </Popover>
        </div>
      </div>
    </motion.header>
  )
})

const NavLink = React.memo(({ item }: { item: NavItem }) => {
  const to = useNavi()
  const { pathname } = useLocation()
  const isActive = item.pathname === pathname

  return (
    <motion.a
      className={ cn(
        'relative px-4 transition-colors cursor-pointer transition-all duration-300',
        isActive
          ? 'text-blue-500 ring-blue-500 ring-1 rounded-full'
          : 'text-gray-600 hover:text-blue-500',
      ) }
      onClick={ () => to(item.pathname as any) }
      data-id={ item.pathname }
      layoutId="navbar-link"
      layout
    >
      { item.name }
    </motion.a>
  )
})

export default NavbarPicBall
