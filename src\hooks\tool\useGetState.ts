import type { SetterFnWittGetLatest, SetterParam, UseGetStateReturn } from './types'
import { deepClone, isFn, isObj } from '@jl-org/tool'

export function useGetState<T, V extends boolean = false>(
  initState: T,
  enableGetter: V = false as V,
): UseGetStateReturn<T, V> {
  const getInitData = useCallback(() => deepClone(initState), [initState])
  const stateRef = useRef<T>(getInitData())
  const [state, setState] = useState<T>(stateRef.current)

  const setter = useCallback((value: SetterParam<T>) => {
    const newVal = value as any

    /** 处理函数类型的 value */
    if (isFn(newVal)) {
      /** 记录值 */
      if (enableGetter) {
        const res = newVal(stateRef.current)

        /** 如果返回值是对象，则自动合并 */
        if (isObj(res)) {
          const merged = { ...stateRef.current, ...res }
          stateRef.current = merged
          setState(merged)

          return
        }

        setState(res)
        stateRef.current = res

        return
      }

      setState((prevState) => {
        const res = newVal(prevState)

        if (isObj(res)) {
          return { ...prevState, ...res }
        }
        return res
      })

      return
    }

    /** 自动合并对象 */
    if (isObj(newVal)) {
      /** 记录值 */
      if (enableGetter) {
        const res = { ...stateRef.current, ...newVal }
        stateRef.current = res
        setState(res)

        return
      }

      setState(prevState => ({ ...prevState, ...newVal }))

      return
    }

    /**
     * 基本数据类型 value
     * 记录值
     */
    if (enableGetter) {
      setState(newVal)
      stateRef.current = newVal
      return
    }

    setState(newVal)
  }, [enableGetter])

  if (enableGetter) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    (setter as SetterFnWittGetLatest<T>).getLatest = useCallback(() => deepClone(stateRef.current), [])
  }

  (setter as SetterFnWittGetLatest<T>).reset = useCallback(() => {
    setState(getInitData())
    stateRef.current = getInitData()
  }, [getInitData])

  return [state, setter] as UseGetStateReturn<T, V>
}
