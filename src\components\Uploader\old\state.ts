import type { FileItem, OldUploaderProps } from '..'
import { blobToBase64, getImg } from '@jl-org/tool'

export function useGenState(
  {
    maxCount,
    maxSize,
    distinct,
    disabled,

    onChange,
    onExceedCount,
    onExceedSize,
    onExceedPixels,
    onPixelsNoMatch,

    accept,
    autoClear = false,
    maxPixels,
    pixels,
  }: OldUploaderProps,
) {
  const [files, setFiles] = useState<FileItem[]>([])
  const [dragActive, setDragActive] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleFiles = async (fileList: File[], e: any) => {
    const newImages: FileItem[] = []

    for (const file of fileList) {
      if (accept && accept.split('/')[1] !== '*') {
        const acceptTypes = accept.split(',')
        const fileType = `.${file.type.split('/').pop()}`
        if (!acceptTypes.includes(fileType)) {
          continue
        }
      }

      if (maxCount && files.length + newImages.length >= maxCount) {
        onExceedCount?.()
        break
      }

      if (maxSize && file.size > maxSize) {
        onExceedSize?.(file.size)
        continue
      }

      if ((maxPixels || pixels) && file.type.startsWith('image/')) {
        const src = URL.createObjectURL(file)
        const img = await getImg(src)
        URL.revokeObjectURL(src)
        if (!img)
          continue

        const { naturalWidth, naturalHeight } = img

        if (maxPixels) {
          if (naturalWidth > maxPixels.width || naturalHeight > maxPixels.height) {
            onExceedPixels?.(naturalWidth, naturalHeight)
            continue
          }
        }
        if (pixels) {
          if (naturalWidth !== pixels.width || naturalHeight !== pixels.height) {
            onPixelsNoMatch?.(naturalWidth, naturalHeight)
            continue
          }
        }
      }

      if (distinct && (
        files.some(item => item.file.name === file.name)
        || newImages.some(item => item.file.name === file.name)
      )) {
        continue
      }

      try {
        const base64 = await blobToBase64(file)
        newImages.push({ file, base64 })
      }
      catch (error) {
        console.error('Failed to convert file to base64:', error)
        continue
      }
    }

    if (newImages.length > 0) {
      setFiles((prev) => {
        const newFileRes = [...prev, ...newImages]
        onChange?.(newFileRes)
        return newFileRes
      })
    }

    e.target.value = ''
    if (autoClear) {
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.value = ''
        }
        setFiles([])
      })
    }
  }

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    if (disabled)
      return
    e.preventDefault()
    e.stopPropagation()

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    }
    else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    if (disabled)
      return
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files?.[0]) {
      handleFiles(Array.from(e.dataTransfer.files), e)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled)
      return
    e.preventDefault()

    if (e.target.files?.[0]) {
      handleFiles(Array.from(e.target.files), e)
    }
  }

  const removeImage = (index: number) => {
    setFiles((prev) => {
      const newFiles = prev.filter((_, i) => i !== index)
      onChange?.(newFiles)
      return newFiles
    })
  }

  return {
    files,
    setFiles,
    dragActive,
    inputRef,

    handleDrag,
    handleDrop,
    handleChange,

    removeImage,
  }
}
