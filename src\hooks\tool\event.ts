import type { WinListenerParams } from '@jl-org/tool'
import type { RefObject } from 'react'
import { bindWinEvent } from '@jl-org/tool'
import { useCallback, useEffect, useLayoutEffect, useState } from 'react'
import { useAsyncEffect } from './lifecycle'
import { useWatchRef } from './state'

export function useOnWinHidden(
  hiddenFn: VoidFunction,
  showFn?: VoidFunction,
) {
  useEffect(() => {
    const fn = () => {
      const isHidden = document.visibilityState === 'hidden'
      if (isHidden) {
        hiddenFn()
        return
      }

      const isVisible = document.visibilityState === 'visible'
      if (isVisible && showFn) {
        showFn()
      }
    }

    document.addEventListener('visibilitychange', fn)

    return () => {
      document.removeEventListener('visibilitychange', fn)
    }
  }, [hiddenFn, showFn])
}

export function useBindWinEvent<K extends keyof WindowEventMap>(
  eventName: K,
  listener: WinListenerParams<K>[1],
  options?: WinListenerParams<K>[2],
) {
  useEffect(() => {
    const unBind = bindWinEvent(eventName, listener, options)
    return unBind
  }, [eventName, listener, options])

  return () => {
    window.removeEventListener(eventName, listener, options)
  }
}

export function useInsertStyle(styleStrOrUrl: string) {
  useAsyncEffect(
    async () => {
      try {
        new URL(styleStrOrUrl)
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = styleStrOrUrl
        document.head.appendChild(link)
        return () => {
          document.head.removeChild(link)
        }
      }
      catch (error) {
      }

      const styleEl = document.createElement('style')
      styleEl.setAttribute('type', 'text/css')
      styleEl.innerHTML = styleStrOrUrl

      document.head.appendChild(styleEl)
      return () => {
        document.head.removeChild(styleEl)
      }
    },
    [styleStrOrUrl],
    {
      effectFn: useInsertionEffect,
    },
  )
}

export function useClickOutside(
  refs: RefObject<HTMLElement>[],
  handler: () => void,
  options: ClickOutsideOpts = {},
) {
  const {
    enabled = true,
    trigger = 'mousedown',
    additionalSelectors = [],
  } = options

  const stableHandler = useWatchRef(handler)

  useEffect(() => {
    if (!enabled)
      return

    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement

      const clickedInRefs = refs.some(ref => ref.current?.contains(target))
      if (clickedInRefs)
        return

      if (additionalSelectors.length > 0) {
        const additionalElements = document.querySelectorAll(additionalSelectors.join(', '))
        const clickedInAdditional = Array.from(additionalElements).some(element =>
          element.contains(target),
        )
        if (clickedInAdditional)
          return
      }

      stableHandler.current?.()
    }

    document.addEventListener(trigger, handleClickOutside)
    return () => {
      document.removeEventListener(trigger, handleClickOutside)
    }
  }, [refs, enabled, trigger, additionalSelectors, stableHandler])
}

export function useScrollBottom(
  elRef: RefObject<HTMLElement>,
  deps: any[] = [],
  options: ScrollBottomOpts = {},
) {
  const {
    enabled = true,
    smooth = false,
    delay = 0,
  } = options

  const scrollToBottom = useCallback(() => {
    const el = elRef.current
    if (!el)
      return

    const { scrollHeight, clientHeight } = el
    if (scrollHeight > clientHeight) {
      const target = scrollHeight - clientHeight
      el.scrollTo({
        top: target,
        behavior: smooth
          ? 'smooth'
          : 'instant',
      })
    }
  }, [elRef, smooth])

  useLayoutEffect(() => {
    if (!enabled)
      return

    if (delay > 0) {
      const timer = window.setTimeout(scrollToBottom, delay)
      return () => window.clearTimeout(timer)
    }
    else {
      scrollToBottom()
    }
  }, [enabled, smooth, delay, scrollToBottom, ...deps])

  return {
    /**
     * 计算并滚动到正确的底部位置
     */
    scrollToBottom,
  }
}

export function useMouse() {
  const [mouse, setMouse] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => setMouse({ x: e.clientX, y: e.clientY })
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  })

  return mouse
}

export function useShortCutKey(opts: ShortCutKeyOpts) {
  const {
    fn,
    key,
    el = window as unknown as HTMLElement,
    ctrl = false,
    shift = false,
    alt = false,
    meta = false,
  } = opts

  const watchFn = useWatchRef(fn)

  useEffect(() => {
    if (!el)
      return

    const handleKeyDown = (e: KeyboardEvent) => {
      const keyEvent = e as KeyboardEvent

      const keyMatches = keyEvent.key.toLowerCase() === key.toLowerCase()
      const ctrlMatches = keyEvent.ctrlKey === ctrl
      const shiftMatches = keyEvent.shiftKey === shift
      const altMatches = keyEvent.altKey === alt
      const metaMatches = keyEvent.metaKey === meta

      if (
        keyMatches
        && ctrlMatches && shiftMatches
        && altMatches && metaMatches
        && watchFn.current
      ) {
        keyEvent.preventDefault()
        watchFn.current(keyEvent)
      }
    }

    el.addEventListener('keydown', handleKeyDown)

    return () => {
      el.removeEventListener('keydown', handleKeyDown)
    }
  }, [alt, ctrl, el, key, meta, shift, watchFn])
}

export type ShortCutKeyOpts = KeyModifier & {
  key: KeyEnum
  fn: (e: KeyboardEvent) => void
  el?: HTMLElement | null
}

export type KeyEnum = ('Ctrl' | 'Shift' | 'Alt' | 'Meta' | 'Enter' | 'Escape' | 'Tab' | 'ArrowUp' | 'ArrowDown' | 'ArrowLeft' | 'ArrowRight' | 'Backspace' | 'Delete' | 'Insert' | 'Home' | 'End' | 'PageUp' | 'PageDown' | 'F1' | 'F2' | 'F3' | 'F4' | 'F5' | 'F6' | 'F7' | 'F8' | 'F9' | 'F10' | 'F11' | 'F12' | 'CapsLock' | 'NumLock' | 'ScrollLock' | 'PrintScreen' | 'Pause' | 'Break' | 'Clear' | 'ContextMenu' | 'Scroll' | 'Unidentified') | (string & {})

export type KeyModifier = {
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
}

interface ClickOutsideOpts {
  enabled?: boolean
  trigger?: 'click' | 'mousedown' | 'contextmenu'
  additionalSelectors?: string[]
}

interface ScrollBottomOpts {
  /**
   * 是否启用
   * @default true
   */
  enabled?: boolean
  smooth?: boolean
  delay?: number
}
