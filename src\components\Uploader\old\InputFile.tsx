import type { CSSProperties, InputHTMLAttributes, ReactNode } from 'react'

import { getImg } from '@jl-org/tool'
import classnames from 'clsx'

const _InputFile = forwardRef<InputFileRef, InputFileProps>((
  {
    style,
    className,

    distinct,
    maxCount,
    maxSize,
    maxPixels,
    autoClear = false,

    onExceedCount,
    onExceedSize,
    onExceedPixels,
    onChange,
    ...rest
  },
  ref,
) => {
  const fileRef = useRef<File[]>([])
  const inputRef = useRef<HTMLInputElement>(null)

  useImperativeHandle(ref, () => ({
    clear: () => {
      fileRef.current = []
      if (inputRef.current) {
        inputRef.current.value = ''
      }
    },
    click: () => {
      inputRef.current?.click()
    },
    getFiles: () => fileRef.current,
  }))

  async function handleChnage(e: React.ChangeEvent<HTMLInputElement>) {
    const files = e.target.files
    const fileRefs = fileRef.current
    if (!files)
      return

    const newFiles: File[] = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      /** 检查拖拽的文件类型 */
      if (rest.accept && rest.accept.split('/')[1] !== '*') {
        const acceptTypes = rest.accept.split(',')
        const fileType = `.${file.type.split('/').pop()}`
        if (!acceptTypes.includes(fileType)) {
          continue
        }
      }

      /** 检查是否超出数量限制 */
      if (maxCount && fileRefs.length + newFiles.length >= maxCount) {
        onExceedCount?.()
        break
      }

      /** 检查文件大小 */
      if (maxSize && file.size > maxSize) {
        onExceedSize?.(file.size)
        continue
      }

      /** 检测像素大小 */
      if (maxPixels && file.type.startsWith('image/')) {
        const src = URL.createObjectURL(file)
        const img = await getImg(src)
        URL.revokeObjectURL(src)
        if (!img)
          continue

        const { naturalWidth, naturalHeight } = img
        if (naturalWidth > maxPixels.width || naturalHeight > maxPixels.height) {
          onExceedPixels?.(naturalWidth, naturalHeight)
          continue
        }
      }

      /** 去重检查 */
      if (distinct && (
        fileRefs.some(item => item.name === file.name)
        || newFiles.some(item => item.name === file.name)
      )) {
        continue
      }

      newFiles.push(file)
    }

    /** 只有在有新文件且未超出限制时才更新 */
    if (newFiles.length > 0) {
      fileRef.current = [...fileRefs, ...newFiles]
      onChange?.(e, fileRef.current)
    }

    inputRef.current!.value = ''
    if (autoClear) {
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.value = ''
        }
        fileRef.current = []
      })
    }
  }

  return <input
    ref={ inputRef }
    className={ classnames(
      'hidden',
      className,
    ) }
    style={ style }
    onChange={ handleChnage }
    type="file"
    { ...rest }
  />
})

/**
 * @deprecated
 */
export const InputFile = memo(_InputFile) as typeof _InputFile
InputFile.displayName = 'InputFile'

export type InputFileProps = {
  className?: string
  style?: CSSProperties
  children?: ReactNode

  distinct?: boolean
  maxCount?: number

  maxSize?: number

  maxPixels?: {
    width: number
    height: number
  }

  autoClear?: boolean

  onChange?: (e: React.ChangeEvent<HTMLInputElement>, files: File[]) => void
  onExceedSize?: (size: number) => void
  onExceedCount?: VoidFunction
  onExceedPixels?: (width: number, height: number) => void
}
& Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'>

export type InputFileRef = {
  clear: VoidFunction
  click: VoidFunction
  getFiles: () => File[]
}
