import React, { memo } from 'react'
import cx from 'clsx'


export const Loader = memo<LoaderProps>((
  {
    style,
    className,
    size = 10,
    ball1Color = 'rgba(127, 186, 229, .68)',
    ball2Color = '#409eff',
  }
) => {
  const styleObj: React.CSSProperties = {
    width: size,
    height: size,
    position: 'absolute',
    borderRadius: '50%',
    transformOrigin: '0px 100%',
    backgroundColor: ball1Color,
  }

  return <div
    className={ cx(
      'animate-spin relative',
      className
    ) }
    style={ {
      width: size * 2,
      height: size * 2,
      ...style
    } }
  >
    <div className='animate-spin' style={ {
      ...styleObj,
    } } />

    <div className='animate-spin' style={ {
      ...styleObj,
      transformOrigin: '0px 50%',
      backgroundColor: ball2Color,
    } } />
  </div>
})


type LoaderProps = {
  className?: string
  style?: React.CSSProperties
  size?: number
  ball1Color?: string
  ball2Color?: string
  children?: React.ReactNode
}