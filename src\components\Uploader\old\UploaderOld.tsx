import type { CSSProperties, InputHTMLAttributes, ReactNode } from 'react'

import type { FileItem } from '../Uploader'
import { excludeKeys } from '@jl-org/tool'
import classnames from 'clsx'
import { Animate } from '../../Animate'
import { useGenState } from './state'

const _Uploader = forwardRef<OldUploaderRef, OldUploaderProps>((props, ref) => {
  const {
    className,
    style,
    previewClassName,
    dragActiveClassName,
    children,

    disabled,
    needPreivew,
    showAcceptedTypesText,
    placeholder = 'Drag and drop your images here, or click to select files',
    onChange,
    ...rest
  } = props

  const {
    files,
    setFiles,
    dragActive,
    inputRef,

    handleDrag,
    handleDrop,
    handleChange,

    removeImage,
  } = useGenState(props)

  useImperativeHandle(ref, () => ({
    clear: () => {
      if (inputRef.current) {
        inputRef.current.value = ''
      }
      setFiles([])
    },
    click: () => {
      inputRef.current?.click()
    },
    getFiles: () => files,
    remove: (index: number) => {
      removeImage(index)
    },
  }))

  const dragAreaClasses = classnames(
    'relative w-full h-full',
    'flex justify-center items-center gap-4 cursor-pointer',
    'transition-colors duration-300 ease-in-out',
    {
      [dragActiveClassName || 'opacity-50']: dragActive,
      '!cursor-not-allowed': disabled,
    },
  )

  return <div
    className={ classnames(
      'mx-auto w-full h-full uploader-container',
      className,
    ) }
    style={ style }>
    <div
      className={ dragAreaClasses }
      onDragEnter={ handleDrag }
      onDragLeave={ handleDrag }
      onDragOver={ handleDrag }
      onDrop={ handleDrop }

      role="button"
      aria-disabled={ disabled }
      onClick={ () => {
        if (disabled)
          return
        inputRef.current?.click()
      } }
    >
      <input
        type="file"
        ref={ inputRef }
        onChange={ handleChange }
        className="hidden"
        { ...excludeKeys(rest, ['autoClear']) }
      />

      { children || <label
        htmlFor="file-upload"
        className="flex flex-col cursor-pointer items-center justify-center"
      >
        <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <p className="mt-2 text-sm text-gray-500">
          { placeholder }
        </p>
      </label> }

    </div>

    { files.length > 0 && needPreivew && (
      <Animate
        className={ classnames('grid grid-cols-3 gap-4 mt-4', previewClassName) }
      >
        { files.map((preview, index) => (
          <Animate
            key={ `${preview.file.name}-${index}` }
            className="group relative"
          >
            <img
              src={ preview.base64 }
              alt={ `Preview ${preview.file.name}` }
              className="h-28 w-full rounded-md object-cover"
            />
            <div
              onClick={ () => removeImage(index) }
              className="absolute right-0 top-0 h-4 w-4 flex cursor-pointer items-center justify-center rounded-full bg-red-500 text-white transition-all duration-500 -mr-2 -mt-2 hover:bg-red-400 focus:outline-none"
            >
              &times;
            </div>
          </Animate>
        )) }
      </Animate>
    ) }

    { rest.accept && showAcceptedTypesText
      && <div className="mt-2 text-center text-sm text-gray-500">
        <p>
          Accepted types:
          { ' ' }
          { rest.accept }
        </p>
      </div> }
  </div>
})

_Uploader.displayName = 'Uploader'

/**
 * @deprecated
 */
export const UploaderOld = memo(_Uploader)

export type OldUploaderProps = {
  disabled?: boolean
  distinct?: boolean
  maxCount?: number
  maxSize?: number
  maxPixels?: {
    width: number
    height: number
  }
  pixels?: {
    width: number
    height: number
  }

  onChange?: (files: FileItem[]) => void
  onExceedSize?: (size: number) => void
  onExceedCount?: VoidFunction
  onExceedPixels?: (width: number, height: number) => void
  onPixelsNoMatch?: (width: number, height: number) => void

  needPreivew?: boolean
  placeholder?: string
  showAcceptedTypesText?: boolean

  autoClear?: boolean

  className?: string
  style?: CSSProperties
  previewClassName?: string
  dragActiveClassName?: string

  children?: ReactNode
}
& Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'>

export type OldUploaderRef = {
  clear: () => void
  click: () => void
  getFiles: () => FileItem[]
  remove: (index: number) => void
}
