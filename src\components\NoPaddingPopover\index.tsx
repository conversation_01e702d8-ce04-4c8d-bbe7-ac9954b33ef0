import { useInsertStyle } from '@/hooks'
import { ConfigProvider, Popover, type PopoverProps } from 'antd'


export const NoPaddingPopover = (
  {
    children,
    ...rest
  }: NoPaddingPopoverProps
) => {

  useInsertStyle(`
    .ant-popover-inner {
      padding: 0 !important;
    }

    .ant-popover-content {
      padding: 0 !important;
    }

    .ant-popover-inner-content {
      padding: 0 !important;
    }
  `)

  return <ConfigProvider
    theme={{
      components: {
        Popover: {
          padding: 0,
          paddingContentHorizontal: 0,
          paddingContentHorizontalLG: 0,
          paddingContentHorizontalSM: 0,
          paddingContentVertical: 0,
          paddingContentVerticalLG: 0,
          paddingContentVerticalSM: 0,
          paddingLG: 0,
          paddingMD: 0,
          paddingSM: 0,
          paddingXL: 0,
          paddingXS: 0,
          paddingXXS: 0,
        }
      }
    }}
  >
    <Popover {...rest}>
      {children}
    </Popover>
  </ConfigProvider>
}

NoPaddingPopover.displayName = 'NoPaddingPopover'

export type NoPaddingPopoverProps = {
  children?: React.ReactNode
}
  & PopoverProps
