import type { FileItem, OldUploaderRef } from '@/components/Uploader'
import type { ShowMode } from '../store'
import type { ChatInputProps } from '../types'
import ModelSvg from '@/assets/svg/model.svg'
import { CostBtn } from '@/components/CostBtn'
import { Select } from '@/components/Select'
import { UploaderOld } from '@/components/Uploader'
import { IS_ZH } from '@/config'
import { god } from '@/god'
import { useT, useToggle } from '@/hooks'
import { Popover } from 'antd'
import cx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { ClipboardList, Image, TrendingUp, Video, X } from 'lucide-react'
import React, { useRef, useState } from 'react'
import { costMap, showModeSortMap, titleMap } from '../constants'
import { chatStore } from '../store'

const iconStyle = { size: 16, strokeWidth: 1.5 }
const Icons = memo<{ disabled?: boolean }>(({ disabled }) => {
  const snap = chatStore.use()
  const items: { Icon: () => React.ReactNode, key: ShowMode }[] = [
    { Icon: () => <ClipboardList { ...iconStyle } />, key: 'copyWrite' },
    { Icon: () => <Image { ...iconStyle } />, key: 'img' },
    { Icon: () => <Video { ...iconStyle } />, key: 'video' },
    { Icon: () => <TrendingUp { ...iconStyle } />, key: 'report' },
    // @ts-ignore
    { Icon: () => <ModelSvg className="size-4" />, key: '3dModel' },
  ]

  return <motion.div
    className="flex gap-4"
    initial={ { opacity: 0, y: 20, width: '0px' } }
    animate={ { opacity: 1, y: 0, width: '145px' } }
    exit={ { opacity: 0, y: 20, width: '0px' } }
    transition={ { duration: 0.3 } }
    layout
  >
    { items.map(item =>
      <Popover
        key={ item.key }
        content={ titleMap()[item.key] }
        className={ cx(
          'cursor-pointer text-gray-600 transition-all duration-300 hover:text-white',
          snap.selectedMode.includes(item.key) && '!text-white',
          { '!cursor-not-allowed': disabled },
        ) }
      >
        <div onClick={ (e) => {
          if (disabled)
            return
          e.stopPropagation()
          if (snap.selectedMode.includes(item.key)) {
            if (snap.selectedMode.length === 1) {
              return god.messageWarn('At least select one mode')
            }
            chatStore.selectedMode = chatStore.selectedMode
              .filter(s => s !== item.key)
              .sort((s1, s2) => showModeSortMap[s1] - showModeSortMap[s2])
            chatStore.showMode = chatStore.selectedMode[0]
          }
          else {
            chatStore.selectedMode = [item.key, ...snap.selectedMode]
              .sort((s1, s2) => showModeSortMap[s1] - showModeSortMap[s2])
            chatStore.showMode = chatStore.selectedMode[0]
          }
        } }>
          { item.Icon() }
        </div>
      </Popover>,
    ) }
  </motion.div>
})

export const ChatInput = memo<ChatInputProps>(({ onSendMsg, isLoading, className, children }) => {
  const t = useT()
  const isZh = IS_ZH
  const snap = chatStore.use()

  const [message, setMessage] = useState('')
  const [selectedFile, setSelectedFile] = useState<FileItem[]>([])
  const uploaderRef = useRef<OldUploaderRef>(null)

  const isAwsMode = snap.mode === 'awsShop'
  const disableSend = useMemo(() => {
    if (
      (selectedFile.length || message.trim()) &&
      !isLoading
    ) {
      return false
    }

    if (!selectedFile.length)
      return true
    if (!message.trim())
      return true
    if (isLoading)
      return true
    return false
  }, [selectedFile, message, isLoading])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (isLoading || (!message.trim() && !selectedFile.length))
      return

    if (isAwsMode && !selectedFile.length) {
      god.messageWarn('You should upload an image first')
      return
    }

    onSendMsg(message, selectedFile.map(item => ({
      ...item,
      rawUrl: '',
    })))
    setMessage('')
    setSelectedFile([])
  }

  const handleFileChange = (files: FileItem[]) => {
    toggleShowIcon(true)
    setSelectedFile(files)
  }

  function getCost() {
    let val = 0
    for (const item of snap.selectedMode) {
      const cost = costMap[item]
      val += cost
    }
    return val
  }

  const [showIcon, toggleShowIcon] = useToggle(false)

  /**
   * init stepStatus
   */
  useEffect(
    () => {
      if (isLoading)
        return
      snap.selectedMode.forEach((item) => {
        chatStore.stepStatus[item] = 'wait'
      })
    },
    [snap.selectedMode],
  )

  return (<div
    className="relative min-w-xl select-none border border-gray-200 rounded-2xl border-solid bg-white p-4 transition-all duration-400"
    style={ {
      boxShadow: isZh
        ? '0 0 5px rgb(23, 126, 223), 0 0 20px #ABCAFC, 0 0 30px #9DCCF9'
        : '0 2px 10px rgba(0, 0, 0, 0.2)',
    } }
  >
    { children }

    <motion.form
      initial={ { y: 20, opacity: 0 } }
      animate={ { y: 0, opacity: 1 } }
      className={ cx(
        'h-26 flex flex-col justify-between',
        className,
      ) }
      onSubmit={ handleSubmit }
    >
      <div className="relative w-full flex items-center gap-2">
        <div className="flex flex-1 items-center gap-2 !w-10">
          {/* Textarea */ }
          <textarea
            rows={ 3 }
            value={ message }
            onChange={ e => setMessage(e.target.value) }
            placeholder={ t('photog.ai-placeholder') }
            className={ cx(
              'bg-transparent outline-none self-start resize-none',
            ) }
            disabled={ isLoading }
            style={ {
              flex: '0 0 calc(100% - 160px)',
            } }
          />

          <div className="absolute right-0 top-13 h-full flex items-center gap-4">
            {/* Upload */ }
            <AnimatePresence>
              { selectedFile.length > 0
                ? <motion.div
                    initial={ { height: 0, opacity: 0 } }
                    animate={ { height: 'auto', opacity: 1 } }
                    exit={ { height: 0, opacity: 0 } }
                    className="relative flex items-center gap-2 overflow-hidden"
                  >
                    <img
                      src={ selectedFile[0]?.base64 }
                      alt="Product Image"
                      className="h-16 rounded-lg object-cover"
                    />

                    <X
                      onClick={ () => setSelectedFile([]) }
                      size={ 14 }
                      className="absolute right-0 top-0 ml-auto cursor-pointer text-red-500 hover:text-red-600"
                    />
                  </motion.div>

                : <Popover
                  content={ <div>{ t('photog.upload-placeholder') }</div> }
                  >
                    <motion.button
                      type="button"
                      onClick={ () => uploaderRef.current?.click() }
                      className={ cx(
                        'size-[2.8rem] flex items-center justify-center text-gray-500 hover:opacity-70 duration-200 transition-all bg-white rounded-lg',
                      ) }
                    >
                      <img width={ 20 } src={ new URL('@/assets/svg/upload-square.svg', import.meta.url).href } alt="" />
                    </motion.button>
                  </Popover> }
            </AnimatePresence>

            <UploaderOld
              autoClear
              ref={ uploaderRef }
              className="hidden"
              onChange={ handleFileChange }
            />

            {/* Send */ }
            <CostBtn
              htmlType="submit"
              className="rounded-lg px-4 py-5"
              // loading={ isLoading }
              disabled={ disableSend || isLoading }
              cost={ getCost() }
            >
              DeepCreate
            </CostBtn>
          </div>
        </div>
      </div>

      {/* Tip */ }
      <div className="flex">
        <Select
          className={ cx(
            'cursor-auto border-gray-200 bg-white !rounded-full',
            'hover:bg-primary hover:text-white',
            { '!bg-primary !text-white': showIcon },
          ) }
          placeholder="DeepCreate"
          placeholderClassName="hover:text-white"
          disabled={ isLoading }
          rotate={ false }
          showIcon={ showIcon }
          // @ts-ignore
          onClick={ () => toggleShowIcon(true) }

          placeholderIcon={ <AnimatePresence>
            { showIcon && <Icons disabled={ !!isLoading } /> }
          </AnimatePresence> }

          options={ [] }
          searchable={ false }
          showEmpty={ false }
          showDownArrow={ false }
        />
      </div>
    </motion.form>
  </div>)
})
