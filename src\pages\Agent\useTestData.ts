import type { ParsedAgentData } from '@/worker/parseAgent'
import type { FileItem } from './types'
import ParseAgentWorker from '@/worker/parseAgent?worker'
import { chatStore } from './store'
import { testData } from './test.data'
import { genMsg } from './tool'

export function useTestData({
  loadingAnswer,
  updateAnswer,
  onFail,
  setIsLoading,
  setMessages,
}: {
  loadingAnswer: Function
  updateAnswer: Function
  onFail: Function
  setIsLoading: Function
  setMessages: Function
}) {
  const workerRef = useRef<Worker | null>(null)
  const answerRef = useRef<string | null>(null)

  const onTestMsg = async (content: string, files: FileItem[]) => {
    /** 开始思考 */
    answerRef.current = loadingAnswer()
    chatStore.openWorkflow = true
    setIsLoading(true)

    workerRef.current?.postMessage(testData)
  }

  useEffect(() => {
    workerRef.current = new ParseAgentWorker()

    /** 监听来自 Worker 的消息 */
    workerRef.current.onmessage = (event: MessageEvent<ParsedAgentData>) => {
      console.log('Received agent data from worker:', event.data)

      const { logEntries, searchResults } = event.data

      // 1. 更新搜索结果到 store
      chatStore.agentSearchRes = searchResults
      chatStore.showMode = 'searchResult'

      // 2. 将日志转换为 HTML 并更新到思考消息中
      if (answerRef.current && logEntries.length > 0) {
        /** 逐条显示日志 */
        let currentIndex = 0
        const processedLogs: typeof logEntries = []

        const renderNextLog = () => {
          if (currentIndex < logEntries.length) {
            /** 添加当前日志到已处理列表 */
            processedLogs.push(logEntries[currentIndex])

            /** 更新显示 */
            updateAnswer(answerRef.current!, 'Agent 处理中...', {
              type: 'embed',
              logs: [...processedLogs], // 传递日志条目数组
            })

            /** 移动到下一条日志 */
            currentIndex++
            requestAnimationFrame(() => setTimeout(renderNextLog, 800))
          }
        }

        /** 开始渲染第一条日志 */
        renderNextLog()
      }

      setIsLoading(false)
    }

    /** 监听 Worker 错误 */
    workerRef.current.onerror = (error) => {
      console.error('Worker error:', error)

      const errorMessage = `<div style="color: #e53e3e; background-color: #fed7d7; padding: 8px; border-radius: 4px; margin: 8px 0;">
      <strong>❌ 错误:</strong> 处理数据时发生错误，请重试。
      <pre style="margin-top: 8px; white-space: pre-wrap;">${error.message || '未知错误'}</pre>
    </div>`

      // @ts-ignore
      setMessages(prev => [
        ...prev,
        genMsg({
          content: errorMessage,
          type: 'embed',
        }),
      ])

      onFail()
      setIsLoading(false)
    }

    return () => {
      workerRef.current?.terminate()
    }
  }, [])

  return {
    onTestMsg,
  }
}
